// components/ModeSelector.tsx

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";

interface ModeSelectorProps {
  currentMode: string;
  onModeChange: (mode: string) => void;
  t: (key: string) => string;
}

const modes = [
  {
    id: "planning",
    icon: "🤰",
  },
  {
    id: "pregnancy",
    icon: "🤱",
  },
  {
    id: "newborn",
    icon: "👶",
  },
];

const ModeSelector: React.FC<ModeSelectorProps> = ({
  currentMode,
  onModeChange,
  t,
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <Text className="text-xl font-semibold mb-4">{t("settings.careMode")}</Text>
        <View className="flex flex-col gap-4">
          {modes.map((mode) => (
            <TouchableOpacity
              key={mode.id}
              className={cn(
                "rounded-lg border p-4 space-y-2",
                currentMode === mode.id
                  ? "bg-primary border-primary"
                  : "bg-background/50 border-border"
              )}
              onPress={() => onModeChange(mode.id)}
            >
              <View className="flex-row items-start space-x-2">
                <Text className="text-2xl">{mode.icon}</Text>
                <View className="flex-1">
                  <Text className={cn("font-medium", currentMode === mode.id ? "text-primary-foreground" : "text-foreground")}>
                    {t(`modes.${mode.id}`)}
                  </Text>
                  <Text
                    className={cn(
                      "text-xs mt-1",
                      currentMode === mode.id
                        ? "text-primary-foreground/80"
                        : "text-muted-foreground"
                    )}
                  >
                    {t(`onboarding.modes.${mode.id}.description`)}
                  </Text>
                </View>
              </View>

              {currentMode === mode.id && (
                <Badge
                  variant="secondary"
                  className="bg-primary-foreground/20 text-primary-foreground text-xs self-start"
                >
                  {t("modes.active")}
                </Badge>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </CardContent>
    </Card>
  );
};

export default ModeSelector;
