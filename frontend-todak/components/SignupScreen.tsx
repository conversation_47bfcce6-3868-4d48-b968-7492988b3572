// components/SignupScreen.tsx

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { ArrowRight, User } from 'lucide-react-native';
import React, { useState } from 'react';
import { ScrollView, Switch, Text, TextInput, View } from 'react-native';

interface SignupScreenProps {
  onComplete: () => void;
  selectedMode: string;
  language: string;
  t: (key: string) => string;
}

const SignupScreen: React.FC<SignupScreenProps> = ({ onComplete, selectedMode, language, t }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    agreeTerms: false,
    agreePrivacy: false,
  });

  const handleSubmit = () => {
    if (formData.name && formData.email && formData.agreeTerms && formData.agreePrivacy) {
      // For native, you may use async storage instead
      onComplete();
    }
  };

  const getModeColor = (mode: string) => {
    const colors: Record<string, string> = {
      planning: 'bg-blue-500',
      pregnancy: 'bg-green-500',
      newborn: 'bg-purple-500',
    };
    return colors[mode] || 'bg-gray-500';
  };

  return (
    <ScrollView className="flex-1 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
      <View className="flex-1 items-center justify-center py-6">
        <Card className="w-full max-w-md shadow-xl">
          <CardHeader className="items-center space-y-4">
            <View className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full items-center justify-center">
              <User className="text-white w-8 h-8" />
            </View>
            <View className="items-center">
              <CardTitle className="text-2xl font-bold">{t('signup.title')}</CardTitle>
              <Text className="text-muted-foreground mt-2 text-center">{t('signup.subtitle')}</Text>
            </View>
            <Badge className={cn(getModeColor(selectedMode), 'text-white')}> 
              {t('signup.selectedMode')}: {t(`modes.${selectedMode}`)}
            </Badge>
          </CardHeader>

          <CardContent className="space-y-4">
            <View className="space-y-2">
              <Text className="text-sm font-medium">{t('signup.name')}</Text>
              <TextInput
                value={formData.name}
                onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                className="border border-border rounded-md px-3 py-2 text-sm bg-background"
                placeholder="Your Name"
              />
            </View>

            <View className="space-y-2">
              <Text className="text-sm font-medium">{t('signup.email')}</Text>
              <TextInput
                value={formData.email}
                onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                className="border border-border rounded-md px-3 py-2 text-sm bg-background"
                placeholder="<EMAIL>"
                keyboardType="email-address"
              />
            </View>

            <View className="space-y-3">
              <View className="flex-row items-center space-x-2">
                <Switch
                  value={formData.agreeTerms}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, agreeTerms: value }))}
                />
                <Text className="text-sm">{t('signup.terms')}</Text>
              </View>

              <View className="flex-row items-center space-x-2">
                <Switch
                  value={formData.agreePrivacy}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, agreePrivacy: value }))}
                />
                <Text className="text-sm">{t('signup.privacy')}</Text>
              </View>
            </View>

            <Button
              onPress={handleSubmit}
              disabled={!formData.name || !formData.email || !formData.agreeTerms || !formData.agreePrivacy}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {t('signup.button')}
              <ArrowRight className="w-4 h-4 ml-2 text-white" />
            </Button>
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
};

export default SignupScreen;
