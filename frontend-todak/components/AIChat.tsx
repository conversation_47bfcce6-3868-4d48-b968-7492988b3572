import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { MessageSquare, Plus } from 'lucide-react-native';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, ScrollView, Text, TextInput, View } from 'react-native';

interface AIChatProps {
  mode: string;
  disabled?: boolean;
}

const modePrompts = {
  planning: [
    'When is the best time to conceive?',
    'What vitamins should I take?',
    'How to track ovulation?',
    'Healthy lifestyle tips',
  ],
  pregnancy: [
    'Is this symptom normal?',
    'Week 20 development',
    'Safe exercises for pregnancy',
    'Nutrition during pregnancy',
  ],
  newborn: [
    'Why is my baby crying?',
    'Sleep schedule help',
    'Feeding frequency',
    'Development milestones',
  ],
};

const mockResponses = {
  planning:
    'Based on your cycle data, your fertile window is typically 5-6 days before ovulation. I recommend tracking your basal body temperature and cervical mucus. Would you like specific nutrition recommendations for preconception health?',
  pregnancy:
    "During week 20, your baby is about the size of a banana! This is an exciting time when you might feel stronger movements. Make sure you're getting enough iron and calcium. Any specific symptoms you'd like me to address?",
  newborn:
    "Crying can indicate hunger, tiredness, discomfort, or overstimulation. Try the 5 S's: swaddling, side position, shushing, swinging, and sucking. If crying persists for more than 2 hours, consider consulting your pediatrician.",
};

const AIChat: React.FC<AIChatProps> = ({ mode, disabled = false }) => {
  const [messages, setMessages] = useState<Array<{ id: number; text: string; isUser: boolean }>>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSend = async (text?: string) => {
    if (disabled) return;
    const messageText = text || input;
    if (!messageText.trim()) return;

    const userMessage = { id: Date.now(), text: messageText, isUser: true };
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        text: mockResponses[mode as keyof typeof mockResponses],
        isUser: false,
      };
      setMessages((prev) => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const currentPrompts = modePrompts[mode as keyof typeof modePrompts] || [];

  return (
    <KeyboardAvoidingView
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView contentContainerStyle={{ padding: 16 }} className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex-row items-center justify-between">
              <View className="flex-row items-center space-x-2">
                <MessageSquare size={20} className="text-primary" />
                <Text className="text-lg font-semibold">AI Health Assistant</Text>
              </View>
              <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                GPT-4 Powered
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <View className="space-y-2">
              <Text className="text-sm font-medium text-muted-foreground">Quick Questions</Text>
              <View className="flex flex-wrap gap-2">
                {currentPrompts.map((prompt, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="justify-start h-auto px-3 py-2 bg-background/50"
                    onPress={() => handleSend(prompt)}
                    disabled={disabled}
                  >
                    <Plus size={16} className="mr-2 text-primary" />
                    <Text className="text-left text-sm">{prompt}</Text>
                  </Button>
                ))}
              </View>
            </View>

            <View className="space-y-3 max-h-96">
              {messages.map((message) => (
                <View
                  key={message.id}
                  className={cn('flex', message.isUser ? 'justify-end' : 'justify-start')}
                >
                  <View
                    className={cn(
                      'max-w-[80%] px-3 py-2 rounded-lg',
                      message.isUser
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    )}
                  >
                    <Text>{message.text}</Text>
                  </View>
                </View>
              ))}
              {isLoading && (
                <View className="flex justify-start">
                  <View className="bg-muted text-muted-foreground px-3 py-2 rounded-lg flex-row space-x-1">
                    <View className="w-2 h-2 bg-primary rounded-full animate-bounce" />
                    <View className="w-2 h-2 bg-primary rounded-full animate-bounce delay-100" />
                    <View className="w-2 h-2 bg-primary rounded-full animate-bounce delay-200" />
                  </View>
                </View>
              )}
              {disabled && (
                <View className="flex justify-center mt-6">
                  <Text className="text-sm text-center text-muted-foreground">
                    AI 채팅 기능은 대시보드에서도 확인하실 수 있습니다.
                  </Text>
                </View>
              )}
            </View>

            <View className="flex-row items-center space-x-2">
              <TextInput
                value={input}
                onChangeText={setInput}
                onSubmitEditing={() => handleSend()}
                placeholder="Ask me anything about your health..."
                className="flex-1 border border-border rounded-md px-3 py-2 text-sm"
                editable={!disabled}
              />
              <Button
                onPress={() => handleSend()}
                disabled={isLoading || !input.trim() || disabled}
              >
                Send
              </Button>
            </View>
          </CardContent>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AIChat;


// mockResponses 실제 API 호출로 대체 
/* 
import { fetchChatGPTResponse } from "@/lib/gpt"; // 추가

type ChatGPTResponse = {
  id: string;
  object: "chat.completion";
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: "assistant" | "user";
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
};


const handleSend = async (text?: string) => {
  if (disabled) return;
  const messageText = text || input;
  if (!messageText.trim()) return;

  const userMessage = { id: Date.now(), text: messageText, isUser: true };
  setMessages(prev => [...prev, userMessage]);
  setInput('');
  setIsLoading(true);

  try {
    const aiText = await fetchChatGPTResponse(messageText, mode);
    const aiResponse = {
      id: Date.now() + 1,
      text: aiText,
      isUser: false
    };
    setMessages(prev => [...prev, aiResponse]);
  } catch (error) {
    setMessages(prev => [...prev, {
      id: Date.now() + 1,
      text: "Sorry, there was an error connecting to GPT.",
      isUser: false
    }]);
    console.error(error);
  }

  setIsLoading(false);
};
 */