import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, Alert } from 'react-native';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select } from '@/components/ui/select';
import { cn } from '@/lib/utils';

const symptoms = [
  { id: 'cramps', label: 'Cramps' },
  { id: 'headache', label: 'Headache' },
  { id: 'fatigue', label: 'Fatigue' },
  { id: 'bloating', label: 'Bloating' },
  { id: 'mood_swings', label: 'Mood Swings' },
  { id: 'acne', label: 'Acne' },
];

const ActivityLogger = () => {
  const [menstruation, setMenstruation] = useState('');
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([]);
  const [condition, setCondition] = useState('');

  const toggleSymptom = (id: string) => {
    setSelectedSymptoms((prev) =>
      prev.includes(id) ? prev.filter((s) => s !== id) : [...prev, id]
    );
  };

  const handleSubmit = () => {
    Alert.alert('Activity logged');
    // You can replace this with actual form handling logic
  };

  return (
    <ScrollView contentContainerStyle={{ padding: 16 }} className="bg-background">
      <Card className="rounded-2xl bg-gradient-to-br from-pink-100 to-purple-100 shadow-lg">
        <CardHeader>
          <CardTitle>Log Your Daily Activity</CardTitle>
          <CardDescription>Keep track of your cycle and well-being.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <View className="space-y-2">
            <Label htmlFor="menstruation">Menstruation</Label>
            <Select
              options={[
                { label: 'Not menstruating', value: 'none' },
                { label: 'Spotting', value: 'spotting' },
                { label: 'Light flow', value: 'light' },
                { label: 'Medium flow', value: 'medium' },
                { label: 'Heavy flow', value: 'heavy' },
              ]}
              selectedValue={menstruation}
              onValueChange={setMenstruation}
              placeholder="Select menstrual flow"
            />
          </View>

          <View className="space-y-2">
            <Label>Symptoms</Label>
            <View className="flex flex-wrap gap-4">
              {symptoms.map((symptom) => (
                <View key={symptom.id} className="flex-row items-center space-x-2 w-1/2">
                  <Checkbox
                    checked={selectedSymptoms.includes(symptom.id)}
                    onValueChange={() => toggleSymptom(symptom.id)}
                    id={symptom.id}
                  />
                  <Label htmlFor={symptom.id} className="font-normal text-sm">
                    {symptom.label}
                  </Label>
                </View>
              ))}
            </View>
          </View>

          <View className="space-y-2">
            <Label htmlFor="condition">Body Condition & Notes</Label>
            <TextInput
              multiline
              numberOfLines={4}
              placeholder="How are you feeling today? Any other symptoms?"
              value={condition}
              onChangeText={setCondition}
              className="border border-border bg-background rounded-md p-2 text-sm text-foreground"
            />
          </View>

          <Button onPress={handleSubmit} className="w-full bg-primary hover:bg-primary/90">
            Log Activity
          </Button>
        </CardContent>
      </Card>
    </ScrollView>
  );
};

export default ActivityLogger;