import { cn } from "@/lib/utils"
import { X } from "lucide-react-native"
import React from "react"
import {
    Dimensions,
    Modal,
    Pressable,
    Text,
    TouchableOpacity,
    View,
} from "react-native"
import {
    Gesture,
    GestureDetector,
    GestureHandlerRootView,
} from "react-native-gesture-handler"
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
} from "react-native-reanimated"

const SCREEN_HEIGHT = Dimensions.get("window").height

export const Sheet = ({
  children,
  open,
  onOpenChange,
}: {
  children: React.ReactNode
  open: boolean
  onOpenChange: (open: boolean) => void
}) => {
  return (
    <Modal transparent visible={open} animationType="fade" onRequestClose={() => onOpenChange(false)}>
      <GestureHandlerRootView className="flex-1">
        <SheetOverlay onPress={() => onOpenChange(false)} />
        {children}
      </GestureHandlerRootView>
    </Modal>
  )
}

export const SheetTrigger = ({
  onPress,
  children,
}: {
  onPress: () => void
  children: React.ReactNode
}) => <TouchableOpacity onPress={onPress}>{children}</TouchableOpacity>

export const SheetClose = ({
  onPress,
  children,
}: {
  onPress?: () => void
  children?: React.ReactNode
}) => (
  <TouchableOpacity onPress={onPress} className="absolute right-4 top-4 opacity-70">
    {children || <X size={20} />}
  </TouchableOpacity>
)

export const SheetOverlay = ({ onPress }: { onPress: () => void }) => (
  <Pressable onPress={onPress} className="absolute inset-0 bg-black/60" />
)

export const SheetContent = ({
  children,
  side = "bottom",
  heightPercent = 0.5,
  onClose,
}: {
  children: React.ReactNode
  side?: "bottom" | "top" | "left" | "right"
  heightPercent?: number
  onClose?: () => void
}) => {
  const translateY = useSharedValue(0)

  const panGesture = Gesture.Pan()
    .onUpdate((e) => {
      translateY.value = Math.max(e.translationY, 0)
    })
    .onEnd(() => {
      if (translateY.value > 100) {
        onClose?.()
      } else {
        translateY.value = withSpring(0)
      }
    })

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }))

  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View
        style={[
          {
            position: "absolute",
            [side]: 0,
            height:
              side === "bottom" || side === "top"
                ? SCREEN_HEIGHT * heightPercent
                : "100%",
            width: side === "left" || side === "right" ? "75%" : "100%",
            backgroundColor: "white",
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            zIndex: 50,
            padding: 16,
          },
          animatedStyle,
        ]}
      >
        <View className="items-center mb-2">
          <View className="h-1 w-10 rounded-full bg-gray-300" />
        </View>
        {children}
      </Animated.View>
    </GestureDetector>
  )
}

export const SheetHeader = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => <View className={cn("mb-4", className)}>{children}</View>

export const SheetFooter = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => <View className={cn("mt-4", className)}>{children}</View>

export const SheetTitle = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => <Text className={cn("text-lg font-semibold", className)}>{children}</Text>

export const SheetDescription = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => <Text className={cn("text-sm text-gray-500", className)}>{children}</Text>
