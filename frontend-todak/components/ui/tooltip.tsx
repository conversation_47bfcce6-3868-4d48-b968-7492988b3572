import { cn } from "@/lib/utils"
import React, { useState } from "react"
import { Pressable, Text, View } from "react-native"

const Tooltip = ({
  children,
  content,
  sideOffset = 8,
  className,
}: {
  children: React.ReactNode
  content: React.ReactNode
  sideOffset?: number
  className?: string
}) => {
  const [visible, setVisible] = useState(false)

  return (
    <View className="relative">
      <TooltipTrigger
        onPressIn={() => setVisible(true)}
        onPressOut={() => setVisible(false)}
      >
        {children}
      </TooltipTrigger>
      {visible && (
        <TooltipContent className={className} sideOffset={sideOffset}>
          {content}
        </TooltipContent>
      )}
    </View>
  )
}

const TooltipTrigger = ({ children, onPressIn, onPressOut }: {
  children: React.ReactNode
  onPressIn?: () => void
  onPressOut?: () => void
}) => {
  return (
    <Pressable onPressIn={onPressIn} onPressOut={onPressOut}>
      {children}
    </Pressable>
  )
}

const TooltipContent = ({
  children,
  sideOffset = 8,
  className,
}: {
  children: React.ReactNode
  sideOffset?: number
  className?: string
}) => {
  return (
    <View
      className={cn(
        "absolute z-50 rounded-md bg-black px-3 py-1.5 text-sm text-white shadow",
        className
      )}
      style={{ top: -sideOffset }}
    >
      <Text>{children}</Text>
    </View>
  )
}

export { Tooltip, TooltipContent, TooltipTrigger }
