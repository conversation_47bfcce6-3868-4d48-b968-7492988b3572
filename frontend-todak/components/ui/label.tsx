// components/ui/Label.tsx

import { cn } from "@/lib/utils";
import React from "react";
import { Text, TextProps } from "react-native";

export interface LabelProps extends TextProps {
  className?: string;
}

export const Label = React.forwardRef<Text, LabelProps>(
  ({ className, ...props }, ref) => {
    return (
      <Text
        ref={ref}
        className={cn(
          "text-sm font-medium leading-none text-foreground opacity-90",
          className
        )}
        {...props}
      />
    );
  }
);

Label.displayName = "Label";
