// components/ui/menubar.tsx

import { Check, ChevronRight, Circle } from "lucide-react-native"
import React, { useState } from "react"
import { Modal, Text, TouchableOpacity, View } from "react-native"

import { cn } from "@/lib/utils"

export const Menubar = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return <View className={cn("flex flex-row items-center space-x-1 rounded-md border bg-background p-1", className)}>{children}</View>
}

export const MenubarMenu = ({ trigger, children }: { trigger: React.ReactNode; children: React.ReactNode }) => {
  const [visible, setVisible] = useState(false)
  return (
    <>
      <TouchableOpacity onPress={() => setVisible(true)}>{trigger}</TouchableOpacity>
      <Modal visible={visible} transparent animationType="fade">
        <TouchableOpacity className="flex-1" onPress={() => setVisible(false)}>
          <View className="absolute top-16 left-4 right-4 rounded-md border bg-white p-2 shadow-md">{children}</View>
        </TouchableOpacity>
      </Modal>
    </>
  )
}

export const MenubarItem = ({ children, onPress, inset }: { children: React.ReactNode; onPress?: () => void; inset?: boolean }) => (
  <TouchableOpacity
    className={cn(
      "flex-row items-center rounded-sm px-2 py-2 text-sm",
      inset && "pl-8"
    )}
    onPress={onPress}
  >
    <Text>{children}</Text>
  </TouchableOpacity>
)

export const MenubarSeparator = () => <View className="my-1 h-px bg-muted" />

export const MenubarLabel = ({ children, inset }: { children: React.ReactNode; inset?: boolean }) => (
  <Text className={cn("px-2 py-1.5 text-sm font-semibold", inset && "pl-8")}>{children}</Text>
)

export const MenubarShortcut = ({ children }: { children: React.ReactNode }) => (
  <Text className="ml-auto text-xs tracking-widest text-muted-foreground">{children}</Text>
)

export const MenubarCheckboxItem = ({ checked, children, onPress }: { checked?: boolean; children: React.ReactNode; onPress?: () => void }) => (
  <TouchableOpacity onPress={onPress} className="flex-row items-center py-2 pl-8 pr-2">
    <View className="absolute left-2 w-4 h-4 items-center justify-center">
      {checked && <Check size={16} />}
    </View>
    <Text>{children}</Text>
  </TouchableOpacity>
)

export const MenubarRadioItem = ({ selected, children, onPress }: { selected?: boolean; children: React.ReactNode; onPress?: () => void }) => (
  <TouchableOpacity onPress={onPress} className="flex-row items-center py-2 pl-8 pr-2">
    <View className="absolute left-2 w-4 h-4 items-center justify-center">
      {selected && <Circle size={10} fill="black" />}
    </View>
    <Text>{children}</Text>
  </TouchableOpacity>
)

export const MenubarSub = ({ trigger, children }: { trigger: React.ReactNode; children: React.ReactNode }) => {
  const [visible, setVisible] = useState(false)
  return (
    <View>
      <TouchableOpacity onPress={() => setVisible(!visible)} className="flex-row items-center px-2 py-1.5">
        {trigger}
        <ChevronRight className="ml-auto h-4 w-4" />
      </TouchableOpacity>
      {visible && <View className="ml-4 mt-1 border-l border-muted pl-2">{children}</View>}
    </View>
  )
}
