// components/ui/Carousel.tsx (with paging & indicator)

import { cn } from "@/lib/utils";
import { ArrowLeft, ArrowRight } from "lucide-react-native";
import React, { createContext, useContext, useEffect, useRef, useState } from "react";
import { Dimensions, Pressable, ScrollView, View } from "react-native";

const { width } = Dimensions.get("window");

interface CarouselContextProps {
  scrollRef: React.RefObject<ScrollView | null>;
  currentIndex: number;
  scrollToIndex: (index: number) => void;
  totalItems: number;
  setTotalItems: React.Dispatch<React.SetStateAction<number>>;
}

const CarouselContext = createContext<CarouselContextProps | null>(null);

export function useCarousel() {
  const context = useContext(CarouselContext);
  if (!context) throw new Error("useCarousel must be used within <Carousel>");
  return context;
}

export const Carousel = ({ children }: { children: React.ReactNode }) => {
  const scrollRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  const scrollToIndex = (index: number) => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({ x: index * width, animated: true });
      setCurrentIndex(index);
    }
  };

  return (
    <CarouselContext.Provider
      value={{ scrollRef, currentIndex, scrollToIndex, totalItems, setTotalItems }}
    >
      <View className="relative">{children}</View>
    </CarouselContext.Provider>
  );
};

export const CarouselContent = ({ children }: { children: React.ReactNode }) => {
  const { scrollRef, setTotalItems, scrollToIndex } = useCarousel();

  useEffect(() => {
    const count = React.Children.count(children);
    setTotalItems(count);
  }, [children]);

  return (
    <ScrollView
      ref={scrollRef}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onMomentumScrollEnd={(event) => {
        const index = Math.round(event.nativeEvent.contentOffset.x / width);
        scrollToIndex(index);
      }}
      className="flex-row w-full"
    >
      {React.Children.map(children, (child, index) => (
        <View key={index} style={{ width }}>{child}</View>
      ))}
    </ScrollView>
  );
};

export const CarouselItem = ({ children }: { children: React.ReactNode }) => (
  <View className="flex-1 items-center justify-center">{children}</View>
);

export const CarouselPrevious = () => {
  const { currentIndex, scrollToIndex } = useCarousel();
  return (
    <Pressable
      onPress={() => scrollToIndex(Math.max(currentIndex - 1, 0))}
      className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
    >
      <ArrowLeft size={24} />
    </Pressable>
  );
};

export const CarouselNext = () => {
  const { currentIndex, totalItems, scrollToIndex } = useCarousel();
  return (
    <Pressable
      onPress={() => scrollToIndex(Math.min(currentIndex + 1, totalItems - 1))}
      className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
    >
      <ArrowRight size={24} />
    </Pressable>
  );
};

export const CarouselIndicator = () => {
  const { currentIndex, totalItems } = useCarousel();
  return (
    <View className="flex-row justify-center mt-2 space-x-2">
      {Array.from({ length: totalItems }).map((_, idx) => (
        <View
          key={idx}
          className={cn(
            "h-2 w-2 rounded-full",
            idx === currentIndex ? "bg-primary" : "bg-gray-400"
          )}
        />
      ))}
    </View>
  );
};