// components/ui/accordion.tsx
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react-native";
import React, { createContext, useContext, useEffect, useState } from "react";
import {
  LayoutAnimation,
  Platform,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from "react-native";

// Enable animation on Android
if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Accordion Context
type AccordionContextType = {
  openItems: string[];
  toggleItem: (id: string) => void;
  multiple: boolean;
};

const AccordionContext = createContext<AccordionContextType>({
  openItems: [],
  toggleItem: () => {},
  multiple: false,
});

type AccordionProps = {
  multiple?: boolean;
  children: React.ReactNode;
};

const Accordion = ({ multiple = false, children }: AccordionProps) => {
  const [openItems, setOpenItems] = useState<string[]>([]);

  const toggleItem = (id: string) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpenItems((prev) =>
      prev.includes(id)
        ? prev.filter((item) => item !== id)
        : multiple
        ? [...prev, id]
        : [id]
    );
  };

  return (
    <AccordionContext.Provider value={{ openItems, toggleItem, multiple }}>
      <View>{children}</View>
    </AccordionContext.Provider>
  );
};

type AccordionItemProps = {
  id: string;
  defaultOpen?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
};

const AccordionItem = ({ id, defaultOpen = false, disabled = false, children }: AccordionItemProps) => {
  const { openItems, toggleItem } = useContext(AccordionContext);
  const isOpen = openItems.includes(id);

  // Set default open on first render
  useEffect(() => {
    if (defaultOpen && !isOpen) {
      toggleItem(id);
    }
  }, []);

  return (
    <AccordionItemContext.Provider value={{ id, isOpen, toggleItem, disabled }}>
      <View className="border-b border-border">{children}</View>
    </AccordionItemContext.Provider>
  );
};

// Sub-context for item
type AccordionItemContextType = {
  id: string;
  isOpen: boolean;
  toggleItem: (id: string) => void;
  disabled: boolean;
};

const AccordionItemContext = createContext<AccordionItemContextType>({
  id: "",
  isOpen: false,
  toggleItem: () => {},
  disabled: false,
});

type AccordionTriggerProps = {
  title: string;
  className?: string;
};

const AccordionTrigger = ({ title, className }: AccordionTriggerProps) => {
  const { id, isOpen, toggleItem, disabled } = useContext(AccordionItemContext);

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={() => toggleItem(id)}
      className={cn(
        "flex-row items-center justify-between py-4",
        disabled && "opacity-50",
        className
      )}
    >
      <Text className="font-medium text-base">{title}</Text>
      <ChevronDown
        size={16}
        className={`transition-transform duration-200 ${isOpen ? "rotate-180" : "rotate-0"}`}
      />
    </TouchableOpacity>
  );
};

const AccordionContent = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  const { isOpen } = useContext(AccordionItemContext);
  if (!isOpen) return null;

  return <View className={cn("pb-4 pt-0", className)}>{children}</View>;
};

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger };

