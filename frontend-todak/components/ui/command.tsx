// components/ui/command.tsx
import { Search } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import {
  FlatList,
  Keyboard,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

type CommandItemType = {
  key: string;
  label: string;
  icon?: React.ReactNode;
  onSelect: () => void;
};

type CommandPaletteProps = {
  visible: boolean;
  onClose: () => void;
  items: CommandItemType[];
  placeholder?: string;
};

export const CommandPalette = ({
  visible,
  onClose,
  items,
  placeholder = "Search...",
}: CommandPaletteProps) => {
  const [search, setSearch] = useState("");

  useEffect(() => {
    if (!visible) setSearch("");
  }, [visible]);

  const filtered = items.filter((item) =>
    item.label.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Modal visible={visible} transparent animationType="fade">
      <TouchableOpacity
        className="flex-1 bg-black/50 justify-center px-4"
        activeOpacity={1}
        onPress={() => {
          Keyboard.dismiss();
          onClose();
        }}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {}}
          className="bg-white dark:bg-black rounded-lg p-4 max-h-[80%]"
        >
          {/* Input */}
          <View className="flex-row items-center border-b border-border px-2 pb-2">
            <Search size={18} className="mr-2 text-muted-foreground" />
            <TextInput
              value={search}
              onChangeText={setSearch}
              placeholder={placeholder}
              className="flex-1 text-base text-foreground"
              autoFocus
            />
          </View>

          {/* List */}
          {filtered.length === 0 ? (
            <Text className="py-6 text-center text-muted-foreground text-sm">
              No results found.
            </Text>
          ) : (
            <FlatList
              data={filtered}
              keyExtractor={(item) => item.key}
              className="mt-2"
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => {
                    item.onSelect();
                    onClose();
                  }}
                  className="px-2 py-3 rounded-md flex-row items-center hover:bg-muted"
                >
                  {item.icon && <View className="mr-2">{item.icon}</View>}
                  <Text className="text-base text-foreground">{item.label}</Text>
                </TouchableOpacity>
              )}
            />
          )}
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};
