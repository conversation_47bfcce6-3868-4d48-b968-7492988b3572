import { cn } from "@/lib/utils"
import React, { useState } from "react"
import { TouchableOpacity } from "react-native"

type ToggleProps = {
  value?: boolean
  onValueChange?: (value: boolean) => void
  children?: React.ReactNode
  className?: string
  variant?: "default" | "outline"
  size?: "default" | "sm" | "lg"
}

export const Toggle = ({
  value,
  onValueChange,
  children,
  className,
  variant = "default",
  size = "default",
}: ToggleProps) => {
  const [internalValue, setInternalValue] = useState(false)
  const isOn = value !== undefined ? value : internalValue

  const handleToggle = () => {
    const newValue = !isOn
    setInternalValue(newValue)
    onValueChange?.(newValue)
  }

  const variantClass = {
    default: "bg-transparent",
    outline: "border border-border bg-transparent",
  }[variant]

  const sizeClass = {
    default: "h-10 px-3",
    sm: "h-9 px-2.5",
    lg: "h-11 px-5",
  }[size]

  const activeClass = isOn
    ? "bg-accent text-accent-foreground"
    : "text-foreground"

  return (
    <TouchableOpacity
      onPress={handleToggle}
      className={cn(
        "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        "disabled:opacity-50",
        variantClass,
        sizeClass,
        activeClass,
        className
      )}
    >
      {children}
    </TouchableOpacity>
  )
}
