// components/ui/dropdown-menu.tsx
import { cn } from "@/lib/utils";
import { Check, Circle } from "lucide-react-native";
import React from "react";
import {
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from "react-native";

type DropdownMenuItem = {
  key: string;
  label: string;
  onPress?: () => void;
  type?: "checkbox" | "radio" | "default";
  checked?: boolean;
  selected?: boolean;
  disabled?: boolean;
};

type DropdownMenuProps = {
  visible: boolean;
  onClose: () => void;
  items: DropdownMenuItem[];
  position?: "bottom" | "top" | "center";
};

export const DropdownMenu = ({
  visible,
  onClose,
  items,
  position = "bottom",
}: DropdownMenuProps) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <TouchableWithoutFeedback onPress={onClose}>
        <View className="flex-1 bg-transparent">
          <TouchableWithoutFeedback>
            <View
              className={cn(
                "absolute z-50 min-w-[160px] bg-white dark:bg-black rounded-md border border-border shadow-lg p-2",
                position === "bottom" && "bottom-16 left-4 right-4",
                position === "top" && "top-16 left-4 right-4",
                position === "center" && "top-1/2 left-4 right-4 -translate-y-1/2"
              )}
            >
              {items.map((item) => (
                <TouchableOpacity
                  key={item.key}
                  onPress={() => {
                    item.onPress?.();
                    onClose();
                  }}
                  disabled={item.disabled}
                  className={cn(
                    "flex-row items-center px-3 py-2 rounded-md",
                    item.disabled ? "opacity-40" : "hover:bg-muted"
                  )}
                >
                  {item.type === "checkbox" && item.checked && (
                    <Check size={16} className="mr-2 text-primary" />
                  )}
                  {item.type === "radio" && item.selected && (
                    <Circle size={10} className="mr-2 text-primary fill-primary" />
                  )}
                  <Text className="text-sm text-foreground">{item.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};
