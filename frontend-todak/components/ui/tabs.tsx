// components/ui/Tabs.tsx

import { cn } from "@/lib/utils";
import React, { useState } from "react";
import { Pressable, Text, View } from "react-native";

type TabsProps = {
  tabs: string[];
  selected: string;
  onChange: (value: string) => void;
  className?: string;
};

export const TabsList = ({ tabs, selected, onChange, className }: TabsProps) => {
  return (
    <View className={cn("flex-row h-10 rounded-md bg-muted p-1", className)}>
      {tabs.map((tab) => (
        <Pressable
          key={tab}
          onPress={() => onChange(tab)}
          className={cn(
            "flex-1 items-center justify-center rounded-sm px-3 py-1.5",
            selected === tab
              ? "bg-background text-foreground shadow-sm"
              : "text-muted-foreground"
          )}
        >
          <Text
            className={cn(
              "text-sm font-medium",
              selected === tab ? "text-foreground" : "text-muted-foreground"
            )}
          >
            {tab}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

type TabsContentProps = {
  value: string;
  selected: string;
  children: React.ReactNode;
  className?: string;
};

export const TabsContent = ({ value, selected, children, className }: TabsContentProps) => {
  if (value !== selected) return null;
  return <View className={cn("mt-2", className)}>{children}</View>;
};

// 전체 Tabs Container
export const Tabs = ({
  defaultValue,
  tabs,
  children,
  className,
}: {
  defaultValue: string;
  tabs: string[];
  children: (selected: string, setSelected: (val: string) => void) => React.ReactNode;
  className?: string;
}) => {
  const [selected, setSelected] = useState(defaultValue);

  return (
    <View className={className}>
      <TabsList tabs={tabs} selected={selected} onChange={setSelected} />
      {children(selected, setSelected)}
    </View>
  );
};
