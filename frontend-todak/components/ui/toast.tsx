// Toast.tsx - React Native + NativeWind Toast
import { cn } from "@/lib/utils"
import { X } from "lucide-react-native"
import React from "react"
import { Animated, Text, TouchableOpacity, View } from "react-native"



export type ToastVariant = "default" | "destructive"

export type ToastActionElement = React.ReactElement

export interface ToastProps {
  id?: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
  variant?: ToastVariant
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
}

export const Toast = React.forwardRef<
  View,
  ToastProps
>(({ variant = "default", children, ...props }, ref) => {
  return (
    <Animated.View
      ref={ref}
      className={cn(
        "m-2 rounded-md border p-4 shadow-lg w-[90%] self-center flex-row items-start",
        variant === "default" && "bg-white border-gray-200",
        variant === "destructive" && "bg-red-600 border-red-700"
      )}
      {...props}
    >
      {children}
    </Animated.View>
  )
})
Toast.displayName = "Toast"

export const ToastProvider = ({ children }: { children: React.ReactNode }) => (
  <View className="absolute top-0 z-50 w-full items-center mt-4">{children}</View>
)

export const ToastTitle = React.forwardRef<
  Text,
  React.ComponentProps<typeof Text> & { className?: string }
>(({ className, children, ...props }, ref) => (
  <Text
    ref={ref}
    className={cn("font-semibold text-sm text-black", className)}
    {...props}
  >
    {children}
  </Text>
))
ToastTitle.displayName = "ToastTitle"

export const ToastDescription = React.forwardRef<
  Text,
  React.ComponentProps<typeof Text> & { className?: string }
>(({ className, children, ...props }, ref) => (
  <Text
    ref={ref}
    className={cn("text-sm mt-1 text-gray-700", className)}
    {...props}
  >
    {children}
  </Text>
))
ToastDescription.displayName = "ToastDescription"

export const ToastClose = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & { className?: string }
>(({ className, ...props }, ref) => (
  <TouchableOpacity
    ref={ref}
    className={cn("ml-2 p-1", className)}
    {...props}
  >
    <X size={18} color="#4b5563" />
  </TouchableOpacity>
))
ToastClose.displayName = "ToastClose"

export const ToastAction = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & { className?: string }
>(({ className, children, ...props }, ref) => (
  <TouchableOpacity
    ref={ref}
    className={cn("ml-2", className)}
    {...props}
  >
    {children}
  </TouchableOpacity>
))
ToastAction.displayName = "ToastAction"
