// Toast.tsx - React Native + NativeWind Toast
import { cn } from "@/lib/utils"
import { X } from "lucide-react-native"
import React from "react"
import { Animated, Dimensions, Text, TouchableOpacity, View } from "react-native"

const SCREEN_WIDTH = Dimensions.get("window").width

export type ToastVariant = "default" | "destructive"

interface ToastProps {
  title: string
  description?: string
  onClose: () => void
  variant?: ToastVariant
  action?: React.ReactNode
}

export const Toast = ({ title, description, onClose, variant = "default", action }: ToastProps) => {
  return (
    <Animated.View
      className={cn(
        "m-2 rounded-md border p-4 shadow-lg w-[90%] self-center flex-row items-start",
        variant === "default" && "bg-white border-gray-200",
        variant === "destructive" && "bg-red-600 border-red-700"
      )}
    >
      <View className="flex-1">
        <Text
          className={cn(
            "font-semibold text-sm",
            variant === "destructive" ? "text-white" : "text-black"
          )}
        >
          {title}
        </Text>
        {description && (
          <Text
            className={cn(
              "text-sm mt-1",
              variant === "destructive" ? "text-red-100" : "text-gray-700"
            )}
          >
            {description}
          </Text>
        )}
      </View>
      {action && <View className="ml-2"> {action} </View>}
      <TouchableOpacity onPress={onClose} className="ml-2 p-1">
        <X size={18} color={variant === "destructive" ? "#fef2f2" : "#4b5563"} />
      </TouchableOpacity>
    </Animated.View>
  )
}

// ToastContainer manages the visible list of toasts
export const ToastContainer = ({ children }: { children: React.ReactNode }) => (
  <View className="absolute top-0 z-50 w-full items-center mt-4">{children}</View>
)
