import { cn } from "@/lib/utils"
import { Dot } from "lucide-react-native"
import React, { useContext } from "react"
import { Text, TextInput, View } from "react-native"

// Stub context and input component (replace with actual logic or OTP library)
const OTPInputContext = React.createContext({
  slots: [] as {
    char: string
    hasFakeCaret: boolean
    isActive: boolean
  }[],
})

const InputOTP = React.forwardRef<any, any>(({ className, containerClassName, ...props }, ref) => (
  <View className={cn("flex flex-row items-center gap-2", containerClassName)}>
    <TextInput
      ref={ref}
      className={cn("opacity-100", className)}
      keyboardType="number-pad"
      {...props}
    />
  </View>
))
InputOTP.displayName = "InputOTP"

const InputOTPGroup = React.forwardRef<any, any>(({ className, ...props }, ref) => (
  <View ref={ref} className={cn("flex flex-row items-center", className)} {...props} />
))
InputOTPGroup.displayName = "InputOTPGroup"

const InputOTPSlot = React.forwardRef<any, { index: number; className?: string }>(({ index, className, ...props }, ref) => {
  const inputOTPContext = useContext(OTPInputContext)
  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index] || {}

  return (
    <View
      ref={ref}
      className={cn(
        "relative h-10 w-10 items-center justify-center border-y border-r border-input text-sm first:rounded-l-md first:border-l last:rounded-r-md",
        isActive && "z-10 border-2 border-blue-500",
        className
      )}
      {...props}
    >
      <Text>{char}</Text>
      {hasFakeCaret && (
        <View className="pointer-events-none absolute inset-0 items-center justify-center">
          <View className="h-4 w-px bg-black animate-pulse" />
        </View>
      )}
    </View>
  )
})
InputOTPSlot.displayName = "InputOTPSlot"

const InputOTPSeparator = React.forwardRef<any, any>((props, ref) => (
  <View ref={ref} role="separator" {...props}>
    <Dot />
  </View>
))
InputOTPSeparator.displayName = "InputOTPSeparator"

export { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot, OTPInputContext }

