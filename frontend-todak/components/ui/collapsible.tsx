// components/ui/Collapsible.tsx
import React, { createContext, ReactNode, useContext, useState } from "react";
import { LayoutAnimation, Platform, TouchableOpacity, UIManager, View } from "react-native";

// Android에서 애니메이션 활성화
if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Context로 열림 상태 공유
type CollapsibleContextType = {
  open: boolean;
  toggle: () => void;
};

const CollapsibleContext = createContext<CollapsibleContextType>({
  open: false,
  toggle: () => {},
});

type CollapsibleProps = {
  children: ReactNode;
  defaultOpen?: boolean;
};

const Collapsible = ({ children, defaultOpen = false }: CollapsibleProps) => {
  const [open, setOpen] = useState(defaultOpen);

  const toggle = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpen((prev) => !prev);
  };

  return (
    <CollapsibleContext.Provider value={{ open, toggle }}>
      <View>{children}</View>
    </CollapsibleContext.Provider>
  );
};

const CollapsibleTrigger = ({
  children,
}: {
  children: ReactNode;
}) => {
  const { toggle } = useContext(CollapsibleContext);
  return <TouchableOpacity onPress={toggle}>{children}</TouchableOpacity>;
};

const CollapsibleContent = ({
  children,
}: {
  children: ReactNode;
}) => {
  const { open } = useContext(CollapsibleContext);
  if (!open) return null;

  return <View>{children}</View>;
};

export { Collapsible, CollapsibleContent, CollapsibleTrigger };

