import { Toggle } from "@/components/ui/toggle"
import { cn } from "@/lib/utils"
import React, { createContext, useContext } from "react"
import { View } from "react-native"

type ToggleGroupContextType = {
  value: string[]
  onValueChange: (val: string[]) => void
  variant?: "default" | "outline"
  size?: "default" | "sm" | "lg"
  type: "single" | "multiple"
}

const ToggleGroupContext = createContext<ToggleGroupContextType>({
  value: [],
  onValueChange: () => {},
  type: "single",
})

type ToggleGroupProps = {
  value?: string[]
  onValueChange?: (val: string[]) => void
  variant?: "default" | "outline"
  size?: "default" | "sm" | "lg"
  type?: "single" | "multiple"
  children: React.ReactNode
  className?: string
}

export const ToggleGroup = ({
  value = [],
  onValueChange = () => {},
  variant = "default",
  size = "default",
  type = "single",
  children,
  className,
}: ToggleGroupProps) => {
  return (
    <ToggleGroupContext.Provider
      value={{ value, onValueChange, variant, size, type }}
    >
      <View className={cn("flex flex-row gap-1", className)}>{children}</View>
    </ToggleGroupContext.Provider>
  )
}

type ToggleGroupItemProps = {
  value: string
  children?: React.ReactNode
  className?: string
}

export const ToggleGroupItem = ({
  value,
  children,
  className,
}: ToggleGroupItemProps) => {
  const { value: selected, onValueChange, type, variant, size } =
    useContext(ToggleGroupContext)

  const isSelected = selected.includes(value)

  const handleChange = () => {
    if (type === "single") {
      onValueChange(isSelected ? [] : [value])
    } else {
      if (isSelected) {
        onValueChange(selected.filter((v) => v !== value))
      } else {
        onValueChange([...selected, value])
      }
    }
  }

  return (
    <Toggle
      value={isSelected}
      onValueChange={handleChange}
      variant={variant}
      size={size}
      className={className}
    >
      {children}
    </Toggle>
  )
}
