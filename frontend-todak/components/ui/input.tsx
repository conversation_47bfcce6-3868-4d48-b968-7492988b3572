// components/ui/Input.tsx

import { cn } from "@/lib/utils";
import React from "react";
import { TextInput, TextInputProps } from "react-native";

export interface InputProps extends TextInputProps {
  className?: string;
}

export const Input = React.forwardRef<TextInput, InputProps>(
  ({ className, ...props }, ref) => {
    return (
      <TextInput
        ref={ref}
        className={cn(
          "h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50",
          className
        )}
        placeholderTextColor="#888" // 앱에서는 명시적으로 지정해야 함
        {...props}
      />
    );
  }
);

Input.displayName = "Input";
