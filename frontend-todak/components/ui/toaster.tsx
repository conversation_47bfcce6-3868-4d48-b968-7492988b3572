import React from "react"
import { View, Text, FlatList } from "react-native"
import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastTitle,
  ToastDescription,
  ToastProvider,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      <FlatList
        data={toasts}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <Toast variant={item.variant}>
            <View className="gap-1">
              {item.title && <ToastTitle>{item.title}</ToastTitle>}
              {item.description && (
                <ToastDescription>{item.description}</ToastDescription>
              )}
            </View>
            {item.action}
            <ToastClose />
          </Toast>
        )}
        contentContainerStyle={{
          position: "absolute",
          bottom: 20,
          right: 20,
          gap: 8,
        }}
      />
    </ToastProvider>
  )
}
