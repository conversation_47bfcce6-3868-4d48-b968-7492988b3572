import {
    Toast,
    ToastClose,
    ToastDescription,
    ToastProvider,
    ToastTitle,
} from "@/components/ui/toast"
import { useToast } from "@/hooks/use-toast"
import React from "react"
import { FlatList, View } from "react-native"

export function Toaster() {
  const { toasts, dismiss } = useToast()

  return (
    <ToastProvider>
      <FlatList
        data={toasts}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <Toast
            variant={item.variant}
            open={item.open}
            onOpenChange={item.onOpenChange}
          >
            <View className="flex-1">
              <View className="gap-1">
                {item.title && <ToastTitle>{item.title}</ToastTitle>}
                {item.description && (
                  <ToastDescription>{item.description}</ToastDescription>
                )}
              </View>
            </View>
            {item.action}
            <ToastClose onPress={() => dismiss(item.id)} />
          </Toast>
        )}
        style={{
          position: "absolute",
          bottom: 20,
          right: 20,
          width: "100%",
        }}
        contentContainerStyle={{
          gap: 8,
          paddingHorizontal: 16,
        }}
      />
    </ToastProvider>
  )
}
