import { cn } from "@/lib/utils";
import React from "react";
import { ActivityIndicator, Pressable, Text, View } from "react-native";
import { tv, type VariantProps } from "tailwind-variants";

export const buttonVariants = tv({
  base: "flex-row items-center justify-center rounded-md text-sm font-medium transition-opacity disabled:opacity-50 disabled:pointer-events-none",
  variants: {
    variant: {
      default: "bg-primary text-primary-foreground",
      destructive: "bg-destructive text-destructive-foreground",
      outline: "border border-input bg-background text-foreground",
      secondary: "bg-secondary text-secondary-foreground",
      ghost: "bg-transparent text-foreground",
      link: "text-primary underline",
    },
    size: {
      default: "h-10 px-4",
      sm: "h-9 px-3 rounded-md",
      lg: "h-11 px-8 rounded-md",
      icon: "h-10 w-10",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  },
});

export interface ButtonProps
  extends React.ComponentPropsWithoutRef<typeof Pressable>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean;
  children: React.ReactNode;
}

export const Button = React.forwardRef<View, ButtonProps>(
  ({ children, isLoading, variant, size, className, ...props }, ref) => {
    const getTextColor = () => {
      switch (variant) {
        case "ghost":
          return "text-foreground";
        case "outline":
          return "text-foreground";
        case "secondary":
          return "text-secondary-foreground";
        case "destructive":
          return "text-destructive-foreground";
        case "link":
          return "text-primary";
        default:
          return "text-primary-foreground";
      }
    };

    return (
      <Pressable
        ref={ref}
        className={cn(buttonVariants({ variant, size }), className)}
        disabled={props.disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <ActivityIndicator color="#ffffff" />
        ) : typeof children === "string" ? (
          <Text className={cn("text-sm font-medium", getTextColor())}>{children}</Text>
        ) : (
          children
        )}
      </Pressable>
    );
  }
);

Button.displayName = "Button";
