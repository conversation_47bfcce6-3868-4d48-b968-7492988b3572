// components/ui/badge.tsx

import { cn } from "@/lib/utils";
import React from "react";
import { Text, View, ViewProps } from "react-native";

export type BadgeVariant = "default" | "secondary" | "destructive" | "outline";

export interface BadgeProps extends ViewProps {
  children: React.ReactNode;
  variant?: BadgeVariant;
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = "default",
  className,
  ...props
}) => {
  const variantStyle = {
    default: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    destructive: "bg-destructive text-destructive-foreground",
    outline: "border border-border text-foreground",
  }[variant];

  return (
    <View
      className={cn(
        "rounded-full px-2.5 py-0.5",
        variantStyle,
        className
      )}
      {...props}
    >
      <Text className="text-xs font-semibold">{children}</Text>
    </View>
  );
};
