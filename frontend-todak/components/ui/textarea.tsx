import { cn } from "@/lib/utils"
import React from "react"
import { TextInput, TextInputProps } from "react-native"

export interface TextareaProps extends TextInputProps {}

export const Textarea = React.forwardRef<TextInput, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <TextInput
        ref={ref}
        multiline
        textAlignVertical="top" // 안드로이드에서 위 정렬
        className={cn(
          "min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-foreground placeholder:text-muted-foreground",
          "focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "disabled:opacity-50",
          className
        )}
        {...props}
      />
    )
  }
)

Textarea.displayName = "Textarea"
/*import React, { useEffect, useRef, useState } from "react"
import {
  Text,
  TextInput,
  TextInputProps,
  TextInput as RNTextInput,
  View,
} from "react-native"
import { cn } from "@/lib/utils"

export interface TextareaProps extends TextInputProps {
  label?: string
  error?: string
  showCounter?: boolean
  autoGrow?: boolean
}

export const Textarea = React.forwardRef<RNTextInput, TextareaProps>(
  (
    {
      className,
      label,
      error,
      showCounter = true,
      autoGrow = true,
      maxLength,
      value,
      onChangeText,
      style,
      ...props
    },
    ref
  ) => {
    const inputRef = useRef<RNTextInput | null>(null)
    const [inputHeight, setInputHeight] = useState(80)
    const [text, setText] = useState(value || "")

    useEffect(() => {
      if (typeof value === "string") {
        setText(value)
      }
    }, [value])

    const handleChange = (textValue: string) => {
      if (onChangeText) onChangeText(textValue)
      setText(textValue)
    }

    return (
      <View className="w-full">
        {label && (
          <Text className="mb-1 text-sm font-medium text-foreground">
            {label}
          </Text>
        )}
        <TextInput
          ref={(node) => {
            if (ref) {
              if (typeof ref === "function") ref(node)
              else (ref as React.MutableRefObject<RNTextInput | null>).current = node
            }
            inputRef.current = node
          }}
          multiline
          textAlignVertical="top"
          maxLength={maxLength}
          onChangeText={handleChange}
          value={text}
          onContentSizeChange={(e) => {
            if (autoGrow) {
              const newHeight = e.nativeEvent.contentSize.height
              setInputHeight(Math.max(80, newHeight))
            }
          }}
          style={[{ height: inputHeight }, style]}
          className={cn(
            "rounded-md border px-3 py-2 text-sm text-foreground bg-background",
            "placeholder:text-muted-foreground",
            "border-input focus:border-ring focus:ring-2 focus:ring-ring focus:ring-offset-2",
            "disabled:opacity-50",
            error ? "border-red-500" : "",
            className
          )}
          {...props}
        />
        {error && (
          <Text className="mt-1 text-xs text-red-500">{error}</Text>
        )}
        {showCounter && typeof maxLength === "number" && (
          <Text className="mt-1 text-xs text-muted-foreground text-right">
            {text.length} / {maxLength}
          </Text>
        )}
      </View>
    )
  }
)

Textarea.displayName = "Textarea"
 */