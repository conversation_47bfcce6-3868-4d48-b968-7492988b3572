// components/ui/Calendar.tsx

import { cn } from "@/lib/utils"; // Tailwind merge helper
import dayjs from "dayjs";
import { ChevronLeft, ChevronRight } from "lucide-react-native";
import React, { useState } from "react";
import { Text, View } from "react-native";
import DatePicker from "react-native-ui-datepicker";

export interface CalendarProps {
  date?: Date;
  onChange?: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

export function Calendar({
  date,
  onChange,
  minDate,
  maxDate,
  className,
}: CalendarProps) {
  const [selectedDate, setSelectedDate] = useState(date || new Date());

  const handleDateChange = (newDate: Date) => {
    setSelectedDate(newDate);
    onChange?.(newDate);
  };

  return (
    <View className={cn("p-4 bg-white rounded-lg border border-gray-200", className)}>
      {/* Custom Header with styled arrows */}
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-semibold text-gray-800">
          {dayjs(selectedDate).format('MMMM YYYY')}
        </Text>
        <View className="flex-row space-x-2">
          <ChevronLeft size={20} color="#6B7280" />
          <ChevronRight size={20} color="#6B7280" />
        </View>
      </View>

      <DatePicker
        mode="single"
        date={selectedDate}
        onChange={(params: any) => {
          if (params.date) {
            const newDate = dayjs(params.date).toDate();
            handleDateChange(newDate);
          }
        }}
        minDate={minDate}
        maxDate={maxDate}
        timePicker={false}
      />
    </View>
  );
}
