// components/ui/Select.tsx

import { cn } from "@/lib/utils";
import { Check, ChevronDown } from "lucide-react-native";
import React, { useState } from "react";
import {
    FlatList,
    Modal,
    Pressable,
    Text,
    TouchableOpacity,
    View,
} from "react-native";

export type Option = {
  label: string;
  value: string;
};

type SelectProps = {
  options: Option[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
};

export const Select = ({
  options,
  value,
  onValueChange,
  placeholder = "Select an option",
  className,
}: SelectProps) => {
  const [open, setOpen] = useState(false);

  const selectedLabel = options.find((opt) => opt.value === value)?.label;

  return (
    <>
      {/* Trigger */}
      <Pressable
        onPress={() => setOpen(true)}
        className={cn(
          "flex-row items-center justify-between h-10 px-3 py-2 border rounded-md border-input bg-background",
          className
        )}
      >
        <Text className="text-sm text-foreground" numberOfLines={1}>
          {selectedLabel || placeholder}
        </Text>
        <ChevronDown size={16} className="text-muted-foreground ml-2" />
      </Pressable>

      {/* Modal Content */}
      <Modal visible={open} transparent animationType="slide">
        <Pressable
          className="flex-1 bg-black/40 justify-end"
          onPress={() => setOpen(false)}
        >
          <View className="bg-popover rounded-t-xl max-h-[50%] p-4">
            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  className={cn(
                    "flex-row items-center justify-between px-3 py-3 rounded-md",
                    item.value === value
                      ? "bg-accent text-accent-foreground"
                      : "bg-transparent"
                  )}
                  onPress={() => {
                    onValueChange(item.value);
                    setOpen(false);
                  }}
                >
                  <Text className="text-sm text-foreground">{item.label}</Text>
                  {item.value === value && <Check size={16} />}
                </TouchableOpacity>
              )}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  );
};
