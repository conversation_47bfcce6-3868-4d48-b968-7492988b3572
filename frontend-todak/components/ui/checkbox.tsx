// components/ui/Checkbox.tsx
import { cn } from "@/lib/utils";
import { Check } from "lucide-react-native";
import React, { useState } from "react";
import { GestureResponderEvent, TouchableOpacity } from "react-native";

type CheckboxProps = {
  checked?: boolean;
  defaultChecked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
};

const Checkbox = ({
  checked,
  defaultChecked = false,
  onChange,
  disabled = false,
  className,
}: CheckboxProps) => {
  const [internalChecked, setInternalChecked] = useState(defaultChecked);
  const isChecked = checked !== undefined ? checked : internalChecked;

  const toggle = (e: GestureResponderEvent) => {
    if (disabled) return;
    const newValue = !isChecked;
    if (checked === undefined) setInternalChecked(newValue);
    onChange?.(newValue);
  };

  return (
    <TouchableOpacity
      onPress={toggle}
      activeOpacity={0.8}
      className={cn(
        "h-5 w-5 items-center justify-center rounded-sm border border-primary bg-white dark:bg-black",
        isChecked ? "bg-primary" : "",
        disabled ? "opacity-50" : "",
        className
      )}
    >
      {isChecked && <Check size={16} color="#fff" />}
    </TouchableOpacity>
  );
};

export { Checkbox };
