import { cn } from "@/lib/utils";
import { AlertTriangle, Info, X } from "lucide-react-native";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Easing,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

type AlertProps = {
  children: React.ReactNode;
  variant?: "default" | "destructive";
  withIcon?: boolean;
  dismissible?: boolean;
  className?: string;
};

const Alert = ({
  children,
  variant = "default",
  withIcon = false,
  dismissible = false,
  className,
}: AlertProps) => {
  const [visible, setVisible] = useState(true);
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: 200,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }).start();
  }, []);

  if (!visible) return null;

  const baseStyle = "relative w-full rounded-lg border p-4 bg-background dark:bg-black";
  const variantStyle =
    variant === "destructive"
      ? "border-destructive/50 text-destructive"
      : "text-foreground border-border";

  const Icon = variant === "destructive" ? AlertTriangle : Info;

  return (
    <Animated.View
      className={cn(baseStyle, variantStyle, className)}
      style={{ opacity }}
    >
      <View className="flex-row items-start">
        {withIcon && (
          <Icon size={20} className="mr-2 mt-1 text-foreground dark:text-white" />
        )}
        <View className="flex-1">{children}</View>
        {dismissible && (
          <TouchableOpacity
            onPress={() => setVisible(false)}
            className="ml-2 p-1"
          >
            <X size={16} className="text-muted-foreground" />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const AlertTitle = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Text className={cn("mb-1 font-medium text-base", className)}>
      {children}
    </Text>
  );
};

const AlertDescription = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Text className={cn("text-sm leading-relaxed", className)}>
      {children}
    </Text>
  );
};

export { Alert, AlertDescription, AlertTitle };

