// components/ui/Breadcrumb.tsx

import { cn } from "@/lib/utils";
import { ChevronRight, MoreHorizontal } from "lucide-react-native";
import React from "react";
import {
    Pressable,
    PressableProps,
    Text,
    TextProps,
    View,
    ViewProps,
} from "react-native";

interface BreadcrumbProps extends ViewProps {
  separator?: React.ReactNode;
}

export const Breadcrumb = React.forwardRef<View, BreadcrumbProps>(
  ({ children, ...props }, ref) => (
    <View ref={ref} accessibilityRole="text" {...props}>
      {children}
    </View>
  )
);
Breadcrumb.displayName = "Breadcrumb";

export const BreadcrumbList = React.forwardRef<View, ViewProps>(
  ({ className, ...props }, ref) => (
    <View
      ref={ref}
      className={cn("flex-row flex-wrap items-center", className)}
      {...props}
    />
  )
);
BreadcrumbList.displayName = "BreadcrumbList";

export const BreadcrumbItem = React.forwardRef<View, ViewProps>(
  ({ className, ...props }, ref) => (
    <View
      ref={ref}
      className={cn("flex-row items-center", className)}
      {...props}
    />
  )
);
BreadcrumbItem.displayName = "BreadcrumbItem";

interface BreadcrumbLinkProps extends PressableProps {
  text: string;
  className?: string;
}

export const BreadcrumbLink = React.forwardRef<View, BreadcrumbLinkProps>(
  ({ text, onPress, className, ...props }, ref) => (
    <Pressable
      ref={ref}
      onPress={onPress}
      className={cn("px-1", className)}
      {...props}
    >
      <Text className="text-primary">{text}</Text>
    </Pressable>
  )
);
BreadcrumbLink.displayName = "BreadcrumbLink";

export const BreadcrumbPage = React.forwardRef<Text, TextProps>(
  ({ children, className, ...props }, ref) => (
    <Text
      ref={ref}
      className={cn("font-medium text-foreground", className)}
      {...props}
    >
      {children}
    </Text>
  )
);
BreadcrumbPage.displayName = "BreadcrumbPage";

export const BreadcrumbSeparator = ({
  children,
  className,
  ...props
}: ViewProps & { children?: React.ReactNode }) => (
  <View className={cn("px-1", className)} {...props}>
    {children ?? <ChevronRight size={16} />}
  </View>
);
BreadcrumbSeparator.displayName = "BreadcrumbSeparator";

export const BreadcrumbEllipsis = ({
  className,
  ...props
}: ViewProps) => (
  <View
    accessibilityLabel="More"
    className={cn("items-center justify-center h-9 w-9", className)}
    {...props}
  >
    <MoreHorizontal size={16} />
  </View>
);
BreadcrumbEllipsis.displayName = "BreadcrumbEllipsis";
