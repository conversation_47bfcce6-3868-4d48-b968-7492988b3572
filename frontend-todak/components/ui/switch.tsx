// components/ui/switch.tsx
import { cn } from "@/lib/utils"
import React from "react"
import { Pressable, View } from "react-native"

type SwitchProps = {
  value: boolean
  onValueChange: (value: boolean) => void
  disabled?: boolean
  className?: string
}

export const Switch = ({
  value,
  onValueChange,
  disabled,
  className,
}: SwitchProps) => {
  return (
    <Pressable
      onPress={() => onValueChange(!value)}
      disabled={disabled}
      className={cn(
        "w-11 h-6 rounded-full border-2 border-transparent flex items-center px-[2px] transition-all",
        value ? "bg-primary" : "bg-input",
        disabled && "opacity-50",
        className
      )}
    >
      <View
        className={cn(
          "w-5 h-5 rounded-full bg-background shadow-md transition-transform",
          value ? "translate-x-5" : "translate-x-0"
        )}
      />
    </Pressable>
  )
}
