// components/ui/popover.tsx

import { cn } from "@/lib/utils"
import React from "react"
import { Dimensions, Modal, Pressable, TouchableOpacity, View } from "react-native"

const SCREEN_WIDTH = Dimensions.get("window").width

const Popover = ({
  open,
  onOpenChange,
  children,
}: {
  open: boolean
  onOpenChange: (state: boolean) => void
  children: React.ReactNode
}) => {
  return <>{children}</>
}

const PopoverTrigger = ({ onPress, children }: { onPress: () => void; children: React.ReactNode }) => {
  return <TouchableOpacity onPress={onPress}>{children}</TouchableOpacity>
}

const PopoverContent = ({
  open,
  onClose,
  className,
  children,
  sideOffset = 8,
}: {
  open: boolean
  onClose: () => void
  className?: string
  children: React.ReactNode
  sideOffset?: number
}) => {
  return (
    <Modal
      transparent
      visible={open}
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable
        className="absolute inset-0 bg-black/40"
        onPress={onClose}
      />
      <View
        className={cn(
          "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-md bg-white p-4 w-[288px] shadow-lg z-50",
          className
        )}
        style={{ marginTop: sideOffset }}
      >
        {children}
      </View>
    </Modal>
  )
}

export { Popover, PopoverContent, PopoverTrigger }

