import { cn } from "@/lib/utils"
import Slider from "@react-native-community/slider"
import React from "react"
import { View } from "react-native"

type NativeSliderProps = {
  value: number
  onValueChange?: (value: number) => void
  minimumValue?: number
  maximumValue?: number
  step?: number
  className?: string
  thumbTintColor?: string
  minimumTrackTintColor?: string
  maximumTrackTintColor?: string
}

export const CustomSlider = ({
  value,
  onValueChange,
  minimumValue = 0,
  maximumValue = 100,
  step = 1,
  className,
  thumbTintColor = "#0F172A", // dark slate
  minimumTrackTintColor = "#3B82F6", // blue-500
  maximumTrackTintColor = "#E5E7EB", // gray-200
}: NativeSliderProps) => {
  return (
    <View className={cn("w-full px-4", className)}>
      <Slider
        style={{ width: "100%", height: 40 }}
        value={value}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        step={step}
        onValueChange={onValueChange}
        thumbTintColor={thumbTintColor}
        minimumTrackTintColor={minimumTrackTintColor}
        maximumTrackTintColor={maximumTrackTintColor}
      />
    </View>
  )
}
