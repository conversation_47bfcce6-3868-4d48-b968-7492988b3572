// components/ui/form.tsx
import { cn } from "@/lib/utils";
import React, { createContext, useContext, useId } from "react";
import { Controller, ControllerProps, FieldPath, FieldValues, FormProvider, useFormContext } from "react-hook-form";
import { Text, TextProps, View, ViewProps } from "react-native";

// ------------------ Context 정의 ------------------
const FormFieldContext = createContext<{ name: string }>({} as any);
const FormItemContext = createContext<{ id: string }>({} as any);
type FormControlProps = {
    children: React.ReactElement<any>  // 또는 React.ReactElement<TextInputProps>
  }
// ------------------ Form 컴포넌트 ------------------
export const Form = FormProvider;

// ------------------ Form<PERSON>ield ------------------
export function FormField<TFieldValues extends FieldValues, T<PERSON><PERSON> extends FieldPath<TFieldValues>>(
  props: ControllerProps<TFieldValues, TName>
) {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
}

// ------------------ useFormField ------------------
export const useFormField = () => {
  const fieldContext = useContext(FormFieldContext);
  const itemContext = useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  if (!fieldContext) throw new Error("useFormField must be used within <FormField>");

  const fieldState = getFieldState(fieldContext.name, formState);
  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-desc`,
    formMessageId: `${id}-msg`,
    ...fieldState,
  };
};

// ------------------ FormItem ------------------
export const FormItem = ({ children, ...props }: ViewProps) => {
  const id = useId?.() || Math.random().toString();
  return (
    <FormItemContext.Provider value={{ id }}>
      <View className="space-y-2" {...props}>
        {children}
      </View>
    </FormItemContext.Provider>
  );
};

// ------------------ FormLabel ------------------
export const FormLabel = ({ children, ...props }: TextProps) => {
  const { formItemId, error } = useFormField();
  return (
    <Text
      accessibilityLabelledBy={formItemId}
      className={cn("text-sm font-medium", error && "text-red-500")}
      {...props}
    >
      {children}
    </Text>
  );
};

// ------------------ FormControl ------------------
export const FormControl = ({ children }: FormControlProps) => {
    const { error, formItemId, formDescriptionId, formMessageId } = useFormField();
  
    return React.cloneElement(children, {
      accessibilityLabel: formItemId,
      accessibilityHint: error ? `${formMessageId}` : `${formDescriptionId}`,
      accessibilityState: { invalid: !!error },
    });
};

// ------------------ FormDescription ------------------
export const FormDescription = ({ children, ...props }: TextProps) => {
  const { formDescriptionId } = useFormField();
  return (
    <Text className="text-sm text-muted-foreground" nativeID={formDescriptionId} {...props}>
      {children}
    </Text>
  );
};

// ------------------ FormMessage ------------------
export const FormMessage = ({ children, ...props }: TextProps) => {
  const { error, formMessageId } = useFormField();
  const body = error?.message || children;
  if (!body) return null;

  return (
    <Text className="text-sm text-red-600 font-medium" nativeID={formMessageId} {...props}>
      {body}
    </Text>
  );
};
