import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { PanelLeft } from "lucide-react-native"
import * as React from "react"
import { Dimensions, Modal, ScrollView, TouchableOpacity, View } from "react-native"

// Simple cva implementation for React Native
type VariantProps<T> = T extends (...args: any[]) => any ? Parameters<T>[0] : never

const cva = (base: string, config?: { variants?: any; defaultVariants?: any }) => {
  return (props?: any) => {
    if (!config || !props) return base

    let classes = base
    if (config.variants && props) {
      Object.keys(config.variants).forEach(key => {
        const variant = props[key] || config.defaultVariants?.[key]
        if (variant && config.variants[key][variant]) {
          classes += ` ${config.variants[key][variant]}`
        }
      })
    }
    return classes
  }
}

// Slot component for React Native
const Slot = React.forwardRef<any, any>(({ children, ...props }, _ref) => {
  if (React.isValidElement(children)) {
    return React.cloneElement(children, { ...props, ...(children.props || {}) })
  }
  return children
})

// Custom hook for mobile detection (React Native version)
const useIsMobile = () => {
  const { width } = Dimensions.get('window');
  return width < 768; // Consider mobile if width < 768px
};

// Simplified Tooltip components for React Native
const TooltipProvider: React.FC<{ children: React.ReactNode; delayDuration?: number }> = ({ children }) => <>{children}</>;
const Tooltip: React.FC<{ children: React.ReactNode }> = ({ children }) => <>{children}</>;
const TooltipTrigger: React.FC<{ children: React.ReactNode; asChild?: boolean }> = ({ children }) => <>{children}</>;
const TooltipContent: React.FC<{ children: React.ReactNode; side?: string; align?: string; hidden?: boolean }> = () => null;



const SIDEBAR_WIDTH = 256 // 16rem = 256px
const SIDEBAR_WIDTH_MOBILE = 288 // 18rem = 288px
const SIDEBAR_WIDTH_ICON = 48 // 3rem = 48px

type SidebarContext = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

const SidebarContext = React.createContext<SidebarContext | null>(null)

function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}

const SidebarProvider = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & {
    defaultOpen?: boolean
    open?: boolean
    onOpenChange?: (open: boolean) => void
    className?: string
  }
>(
  (
    {
      defaultOpen = true,
      open: openProp,
      onOpenChange: setOpenProp,
      className,
      style,
      children,
      ...props
    },
    ref
  ) => {
    const isMobile = useIsMobile()
    const [openMobile, setOpenMobile] = React.useState(false)

    // This is the internal state of the sidebar.
    // We use openProp and setOpenProp for control from outside the component.
    const [_open, _setOpen] = React.useState(defaultOpen)
    const open = openProp ?? _open
    const setOpen = React.useCallback(
      (value: boolean | ((value: boolean) => boolean)) => {
        const openState = typeof value === "function" ? value(open) : value
        if (setOpenProp) {
          setOpenProp(openState)
        } else {
          _setOpen(openState)
        }

        // React Native doesn't have document.cookie, use AsyncStorage instead
        // AsyncStorage.setItem(SIDEBAR_COOKIE_NAME, openState.toString())
      },
      [setOpenProp, open]
    )

    // Helper to toggle the sidebar.
    const toggleSidebar = React.useCallback(() => {
      return isMobile
        ? setOpenMobile((open) => !open)
        : setOpen((open) => !open)
    }, [isMobile, setOpen, setOpenMobile])

    // React Native doesn't support keyboard shortcuts in the same way
    // This effect is kept for potential future implementation
    React.useEffect(() => {
      // In React Native, keyboard shortcuts would need to be handled differently
      // For now, we'll skip this functionality
    }, [toggleSidebar])

    // We add a state so that we can do data-state="expanded" or "collapsed".
    // This makes it easier to style the sidebar with Tailwind classes.
    const state = open ? "expanded" : "collapsed"

    const contextValue = React.useMemo<SidebarContext>(
      () => ({
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar,
      }),
      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]
    )

    return (
      <SidebarContext.Provider value={contextValue}>
        <TooltipProvider delayDuration={0}>
          <View
            style={[
              {
                width: '100%',
                minHeight: '100%',
              },
              style,
            ]}
            className={cn(
              "group/sidebar-wrapper flex min-h-screen w-full",
              className
            )}
            ref={ref}
            {...props}
          >
            {children}
          </View>
        </TooltipProvider>
      </SidebarContext.Provider>
    )
  }
)
SidebarProvider.displayName = "SidebarProvider"

const Sidebar = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & {
    side?: "left" | "right"
    variant?: "sidebar" | "floating" | "inset"
    collapsible?: "offcanvas" | "icon" | "none"
    className?: string
  }
>(
  (
    {
      side = "left",
      variant = "sidebar",
      collapsible = "offcanvas",
      className,
      children,
      ...props
    },
    ref
  ) => {
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()

    if (collapsible === "none") {
      return (
        <View
          className={cn(
            "flex h-full flex-col bg-sidebar text-sidebar-foreground",
            className
          )}
          style={{ width: SIDEBAR_WIDTH }}
          ref={ref}
          {...props}
        >
          {children}
        </View>
      )
    }

    if (isMobile) {
      return (
        <Modal visible={openMobile} onRequestClose={() => setOpenMobile(false)} animationType="slide">
          <View
            className="flex-1 bg-sidebar"
            style={{ width: SIDEBAR_WIDTH_MOBILE }}
          >
            <View className="flex-1 flex-col p-0">
              {children}
            </View>
          </View>
        </Modal>
      )
    }

    return (
      <View
        ref={ref}
        className="text-sidebar-foreground"
        style={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          [side]: 0,
          width: state === "collapsed" ? SIDEBAR_WIDTH_ICON : SIDEBAR_WIDTH,
          zIndex: 10,
        }}
      >
        <View
          className={cn(
            "flex h-full w-full flex-col bg-sidebar",
            variant === "floating" && "rounded-lg border border-sidebar-border",
            className
          )}
          {...props}
        >
          {children}
        </View>
      </View>
    )
  }
)
Sidebar.displayName = "Sidebar"

interface SidebarTriggerProps {
  className?: string;
  onPress?: () => void;
}

const SidebarTrigger = React.forwardRef<View, SidebarTriggerProps>(
  ({ className, onPress, ...props }, ref) => {
    const { toggleSidebar } = useSidebar()

    return (
      <Button
        ref={ref}
        variant="ghost"
        size="icon"
        className={cn("h-7 w-7", className)}
        onPress={() => {
          onPress?.()
          toggleSidebar()
        }}
        {...props}
      >
        <PanelLeft />
      </Button>
    )
  }
)
SidebarTrigger.displayName = "SidebarTrigger"

const SidebarRail = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & { className?: string }
>(({ className, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()

  return (
    <TouchableOpacity
      ref={ref}
      onPress={toggleSidebar}
      className={cn(
        "absolute inset-y-0 z-20 w-4 transition-all ease-linear",
        className
      )}
      {...props}
    />
  )
})
SidebarRail.displayName = "SidebarRail"

const SidebarInset = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <View
      ref={ref}
      className={cn(
        "relative flex min-h-screen flex-1 flex-col bg-background",
        className
      )}
      {...props}
    />
  )
})
SidebarInset.displayName = "SidebarInset"

const SidebarInput = React.forwardRef<
  any,
  React.ComponentProps<typeof Input>
>(({ className, ...props }, ref) => {
  return (
    <Input
      ref={ref}
      data-sidebar="input"
      className={cn(
        "h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",
        className
      )}
      {...props}
    />
  )
})
SidebarInput.displayName = "SidebarInput"

const SidebarHeader = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <View
      ref={ref}
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
})
SidebarHeader.displayName = "SidebarHeader"

const SidebarFooter = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <View
      ref={ref}
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
})
SidebarFooter.displayName = "SidebarFooter"

const SidebarSeparator = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <View
      ref={ref}
      className={cn("mx-2 w-auto bg-sidebar-border h-px", className)}
      {...props}
    />
  )
})
SidebarSeparator.displayName = "SidebarSeparator"

const SidebarContent = React.forwardRef<
  ScrollView,
  React.ComponentProps<typeof ScrollView> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <ScrollView
      ref={ref}
      className={cn(
        "flex-1 flex-col gap-2",
        className
      )}
      {...props}
    />
  )
})
SidebarContent.displayName = "SidebarContent"

const SidebarGroup = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => {
  return (
    <View
      ref={ref}
      className={cn("relative flex w-full min-w-0 flex-col p-2", className)}
      {...props}
    />
  )
})
SidebarGroup.displayName = "SidebarGroup"

const SidebarGroupLabel = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { asChild?: boolean; className?: string }
>(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : View

  return (
    <Comp
      ref={ref}
      className={cn(
        "duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupLabel.displayName = "SidebarGroupLabel"

const SidebarGroupAction = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & { asChild?: boolean; className?: string }
>(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : TouchableOpacity

  return (
    <Comp
      ref={ref}
      className={cn(
        "absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupAction.displayName = "SidebarGroupAction"

const SidebarGroupContent = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn("w-full text-sm", className)}
    {...props}
  />
))
SidebarGroupContent.displayName = "SidebarGroupContent"

const SidebarMenu = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn("flex w-full min-w-0 flex-col gap-1", className)}
    {...props}
  />
))
SidebarMenu.displayName = "SidebarMenu"

const SidebarMenuItem = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn("group/menu-item relative", className)}
    {...props}
  />
))
SidebarMenuItem.displayName = "SidebarMenuItem"

const sidebarMenuButtonVariants = cva(
  "peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
        outline:
          "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]",
      },
      size: {
        default: "h-8 text-sm",
        sm: "h-7 text-xs",
        lg: "h-12 text-sm group-data-[collapsible=icon]:!p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const SidebarMenuButton = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & {
    asChild?: boolean
    isActive?: boolean
    tooltip?: string | React.ComponentProps<typeof TooltipContent>
    className?: string
  } & VariantProps<typeof sidebarMenuButtonVariants>
>(
  (
    {
      asChild = false,
      isActive = false,
      variant = "default",
      size = "default",
      tooltip,
      className,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : TouchableOpacity
    const { isMobile, state } = useSidebar()

    const button = (
      <Comp
        ref={ref}
        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}
        {...props}
      />
    )

    if (!tooltip) {
      return button
    }

    if (typeof tooltip === "string") {
      tooltip = {
        children: tooltip,
      }
    }

    return (
      <Tooltip>
        <TooltipTrigger asChild>{button}</TooltipTrigger>
        <TooltipContent
          side="right"
          align="center"
          hidden={state !== "collapsed" || isMobile}
          {...tooltip}
        />
      </Tooltip>
    )
  }
)
SidebarMenuButton.displayName = "SidebarMenuButton"

const SidebarMenuAction = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & {
    asChild?: boolean
    showOnHover?: boolean
    className?: string
  }
>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {
  const Comp = asChild ? Slot : TouchableOpacity

  return (
    <Comp
      ref={ref}
      className={cn(
        "absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuAction.displayName = "SidebarMenuAction"

const SidebarMenuBadge = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn(
      "absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground",
      className
    )}
    {...props}
  />
))
SidebarMenuBadge.displayName = "SidebarMenuBadge"

const SidebarMenuSkeleton = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & {
    showIcon?: boolean
    className?: string
  }
>(({ className, showIcon = false, ...props }, ref) => {
  // Random width between 50 to 90%.
  const widthPercentage = React.useMemo(() => {
    return Math.floor(Math.random() * 40) + 50
  }, [])

  return (
    <View
      ref={ref}
      className={cn("rounded-md h-8 flex-row gap-2 px-2 items-center", className)}
      {...props}
    >
      {showIcon && (
        <Skeleton
          className="w-4 h-4 rounded-md"
        />
      )}
      <View style={{ flex: 1, maxWidth: `${widthPercentage}%` }}>
        <Skeleton
          className="h-4 w-full"
        />
      </View>
    </View>
  )
})
SidebarMenuSkeleton.displayName = "SidebarMenuSkeleton"

const SidebarMenuSub = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn(
      "mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5",
      className
    )}
    {...props}
  />
))
SidebarMenuSub.displayName = "SidebarMenuSub"

const SidebarMenuSubItem = React.forwardRef<
  View,
  React.ComponentProps<typeof View> & { className?: string }
>(({ ...props }, ref) => <View ref={ref} {...props} />)
SidebarMenuSubItem.displayName = "SidebarMenuSubItem"

const SidebarMenuSubButton = React.forwardRef<
  View,
  React.ComponentProps<typeof TouchableOpacity> & {
    asChild?: boolean
    size?: "sm" | "md"
    isActive?: boolean
    className?: string
  }
>(({ asChild = false, size = "md", isActive, className, ...props }, ref) => {
  const Comp = asChild ? Slot : TouchableOpacity

  return (
    <Comp
      ref={ref}
      className={cn(
        "flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring",
        "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",
        size === "sm" && "text-xs",
        size === "md" && "text-sm",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuSubButton.displayName = "SidebarMenuSubButton"

export {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupAction,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInput,
    SidebarInset,
    SidebarMenu,
    SidebarMenuAction,
    SidebarMenuBadge,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSkeleton,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarProvider,
    SidebarRail,
    SidebarSeparator,
    SidebarTrigger,
    useSidebar
}

