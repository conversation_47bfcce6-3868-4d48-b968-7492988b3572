// components/ui/hover-card.tsx
import React, { useRef, useState } from "react"
import {
    Animated,
    Easing,
    Modal,
    Pressable,
    TouchableOpacity
} from "react-native"
import { twMerge } from "tailwind-merge"; // or use `cn()` from nativewind

type HoverCardProps = {
  children: React.ReactNode
  content: React.ReactNode
  className?: string
  triggerClassName?: string
}

export const HoverCard = ({
  children,
  content,
  className,
  triggerClassName,
}: HoverCardProps) => {
  const [visible, setVisible] = useState(false)
  const opacity = useRef(new Animated.Value(0)).current

  const show = () => {
    setVisible(true)
    Animated.timing(opacity, {
      toValue: 1,
      duration: 150,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }).start()
  }

  const hide = () => {
    Animated.timing(opacity, {
      toValue: 0,
      duration: 100,
      easing: Easing.in(Easing.ease),
      useNativeDriver: true,
    }).start(() => setVisible(false))
  }

  return (
    <>
      <TouchableOpacity
        onPressIn={show}
        onPressOut={hide}
        className={triggerClassName}
      >
        {children}
      </TouchableOpacity>

      <Modal transparent visible={visible} animationType="none">
        <Pressable style={{ flex: 1 }} onPressOut={hide}>
          <Animated.View
            style={{
              opacity,
              transform: [
                {
                  scale: opacity.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.95, 1],
                  }),
                },
              ],
            }}
            className={twMerge(
              "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-64 rounded-md bg-white p-4 shadow-lg",
              className
            )}
          >
            {content}
          </Animated.View>
        </Pressable>
      </Modal>
    </>
  )
}
