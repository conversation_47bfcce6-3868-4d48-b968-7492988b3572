// components/ui/alert-dialog.tsx
import React, { createContext, useContext, useState } from "react";
import {
  Modal,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from "react-native";

// Context to share dialog control
const AlertDialogContext = createContext<{
  visible: boolean;
  setVisible: (v: boolean) => void;
}>({
  visible: false,
  setVisible: () => {},
});

const AlertDialog = ({ children }: { children: React.ReactNode }) => {
  const [visible, setVisible] = useState(false);
  return (
    <AlertDialogContext.Provider value={{ visible, setVisible }}>
      {children}
    </AlertDialogContext.Provider>
  );
};

const AlertDialogTrigger = ({ children }: { children: React.ReactNode }) => {
  const { setVisible } = useContext(AlertDialogContext);
  return (
    <TouchableOpacity onPress={() => setVisible(true)}>{children}</TouchableOpacity>
  );
};

const AlertDialogContent = ({ children }: { children: React.ReactNode }) => {
  const { visible, setVisible } = useContext(AlertDialogContext);

  return (
    <Modal transparent visible={visible} animationType="fade">
      <TouchableWithoutFeedback onPress={() => setVisible(false)}>
        <View className="flex-1 bg-black/50 justify-center items-center px-6">
          <TouchableWithoutFeedback>
            <View className="bg-white dark:bg-black w-full max-w-lg rounded-lg p-6 shadow-lg">
              {children}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const AlertDialogHeader = ({ children }: { children: React.ReactNode }) => (
  <View className="mb-4">{children}</View>
);

const AlertDialogFooter = ({ children }: { children: React.ReactNode }) => (
  <View className="flex flex-col-reverse space-y-2 space-y-reverse sm:flex-row sm:justify-end sm:space-x-2">
    {children}
  </View>
);

const AlertDialogTitle = ({ children }: { children: React.ReactNode }) => (
  <Text className="text-lg font-semibold text-center mb-2">{children}</Text>
);

const AlertDialogDescription = ({ children }: { children: React.ReactNode }) => (
  <Text className="text-sm text-muted-foreground text-center">{children}</Text>
);

const AlertDialogCancel = ({ children }: { children: React.ReactNode }) => {
  const { setVisible } = useContext(AlertDialogContext);
  return (
    <TouchableOpacity
      onPress={() => setVisible(false)}
      className="mt-2 px-4 py-2 rounded-md border border-gray-300 bg-white dark:bg-black"
    >
      <Text className="text-center text-gray-800 dark:text-white">{children}</Text>
    </TouchableOpacity>
  );
};

const AlertDialogAction = ({
  children,
  onPress,
}: {
  children: React.ReactNode;
  onPress?: () => void;
}) => {
  const { setVisible } = useContext(AlertDialogContext);
  return (
    <TouchableOpacity
      onPress={() => {
        onPress?.();
        setVisible(false);
      }}
      className="mt-2 px-4 py-2 rounded-md bg-primary"
    >
      <Text className="text-center text-white">{children}</Text>
    </TouchableOpacity>
  );
};

export {
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger
};

