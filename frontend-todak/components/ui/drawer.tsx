// components/ui/drawer.tsx
import { cn } from "@/lib/utils";
import { X } from "lucide-react-native";
import React from "react";
import {
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from "react-native";

type DrawerProps = {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
};

export const Drawer = ({ visible, onClose, children }: DrawerProps) => {
  return (
    <Modal visible={visible} animationType="slide" transparent>
      <TouchableWithoutFeedback onPress={onClose}>
        <View className="flex-1 bg-black/50 justify-end">
          <TouchableWithoutFeedback>
            <View className="bg-background rounded-t-2xl p-4 max-h-[90%]">
              {/* Handle Bar */}
              <View className="mx-auto mb-4 h-2 w-20 rounded-full bg-muted" />
              {children}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export const DrawerHeader = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <View className={cn("mb-2 px-2", className)}>{children}</View>
);

export const DrawerFooter = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <View className={cn("mt-auto gap-2 px-2", className)}>{children}</View>
);

export const DrawerTitle = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <Text className={cn("text-lg font-semibold leading-tight", className)}>
    {children}
  </Text>
);

export const DrawerDescription = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <Text className={cn("text-sm text-muted-foreground", className)}>
    {children}
  </Text>
);

export const DrawerClose = ({
  onPress,
  className,
}: {
  onPress: () => void;
  className?: string;
}) => (
  <TouchableOpacity
    onPress={onPress}
    className={cn("absolute right-4 top-4 p-1", className)}
  >
    <X size={20} className="text-muted-foreground" />
  </TouchableOpacity>
);
