// components/ui/context-menu.tsx
import { cn } from "@/lib/utils";
import { Check, Circle } from "lucide-react-native";
import React from "react";
import {
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from "react-native";

export type MenuItem = {
  key: string;
  label: string;
  onPress?: () => void;
  disabled?: boolean;
  icon?: React.ReactNode;
  type?: "checkbox" | "radio" | "default";
  checked?: boolean;
  selected?: boolean;
};

type ContextMenuProps = {
  items: MenuItem[];
  visible: boolean;
  onClose: () => void;
};

export const ContextMenu = ({ items, visible, onClose }: ContextMenuProps) => {
  return (
    <Modal transparent animationType="fade" visible={visible}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View className="flex-1 bg-black/30 justify-center items-center px-8">
          <TouchableWithoutFeedback>
            <View className="bg-white dark:bg-black w-full rounded-md p-2 max-w-sm shadow-lg">
              {items.map((item) => (
                <TouchableOpacity
                  key={item.key}
                  disabled={item.disabled}
                  onPress={() => {
                    item.onPress?.();
                    onClose();
                  }}
                  className={cn(
                    "flex-row items-center px-3 py-2 rounded-md",
                    item.disabled
                      ? "opacity-50"
                      : "active:bg-muted focus:bg-muted"
                  )}
                >
                  {/* Left Icon / Checkmark */}
                  {item.type === "checkbox" && item.checked && (
                    <Check size={16} className="mr-2 text-primary" />
                  )}
                  {item.type === "radio" && item.selected && (
                    <Circle size={10} className="mr-2 fill-primary" />
                  )}
                  {!item.type && item.icon && (
                    <View className="mr-2">{item.icon}</View>
                  )}

                  <Text className="text-base text-foreground">{item.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};
