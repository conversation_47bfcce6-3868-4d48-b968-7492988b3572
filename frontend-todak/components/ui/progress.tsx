import { cn } from "@/lib/utils"
import React from "react"
import { View } from "react-native"

type ProgressProps = {
  value?: number
  className?: string
  indicatorClassName?: string
}

const Progress = ({
  value = 0,
  className,
  indicatorClassName,
}: ProgressProps) => {
  const clampedValue = Math.max(0, Math.min(value, 100))

  return (
    <View
      accessibilityRole="progressbar"
      accessibilityValue={{ now: clampedValue, min: 0, max: 100 }}
      className={cn("relative h-4 w-full overflow-hidden rounded-full bg-secondary", className)}
    >
      <View
        className={cn("h-full bg-primary absolute left-0", indicatorClassName)}
        style={{ width: `${clampedValue}%` }}
      />
    </View>
  )
}

export { Progress }
