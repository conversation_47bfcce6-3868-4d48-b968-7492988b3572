import { cn } from "@/lib/utils";
import React, { useState } from "react";
import { Image, StyleSheet, Text, View, ViewProps } from "react-native";

export interface AvatarProps extends ViewProps {
  uri?: string;
  fallback?: string;
  size?: number;
  badgeText?: string; // 배지 텍스트 예: "✓", "3", "🔴"
}

export const Avatar = ({
  uri,
  fallback = "?",
  size = 40,
  className,
  badgeText,
  style,
  ...props
}: AvatarProps) => {
  const [hasError, setHasError] = useState(false);

  const avatarStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
  };

  return (
    <View
      className={cn("bg-muted items-center justify-center", className)}
      style={[avatarStyle, style]}
      {...props}
    >
      {!uri || hasError ? (
        <Text className="text-foreground text-base font-medium">{fallback}</Text>
      ) : (
        <Image
          source={{ uri }}
          style={[StyleSheet.absoluteFillObject, avatarStyle]}
          onError={() => setHasError(true)}
        />
      )}

      {/* 🔴 Badge Layer */}
      {badgeText && (
        <View
          style={{
            position: "absolute",
            bottom: -2,
            right: -2,
            backgroundColor: "#f43f5e",
            borderRadius: 8,
            paddingHorizontal: 4,
            paddingVertical: 2,
            minWidth: 16,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Text style={{ fontSize: 10, color: "white", fontWeight: "bold" }}>{badgeText}</Text>
        </View>
      )}
    </View>
  );
};
