import { cn } from "@/lib/utils"
import React from "react"
import { View } from "react-native"

type SeparatorProps = {
  orientation?: "horizontal" | "vertical"
  className?: string
}

export const Separator = ({
  orientation = "horizontal",
  className,
}: SeparatorProps) => {
  return (
    <View
      accessible={false}
      className={cn(
        "bg-border",
        orientation === "horizontal" ? "h-px w-full" : "w-px h-full",
        className
      )}
    />
  )
}
