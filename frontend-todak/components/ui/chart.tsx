import { cn } from "@/lib/utils";
import * as React from "react";
import { Dimensions, Text, View } from "react-native";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-native-chart-kit";


const THEMES = { light: "", dark: ".dark" } as const;

export type ChartConfig = {
  [k in string]: {
    label?: React.ReactNode;
    icon?: React.ComponentType;
  } & (
    | { color?: string; theme?: never }
    | { color?: never; theme: Record<keyof typeof THEMES, string> }
  );
};

type ChartContextProps = {
  config: ChartConfig;
};

const ChartContext = React.createContext<ChartContextProps | null>(null);

function useChart() {
  const context = React.useContext(ChartContext);
  if (!context) throw new Error("useChart must be used within a <ChartContainer />");
  return context;
}

interface ChartContainerProps {
  className?: string;
  children: React.ReactNode;
  config: ChartConfig;
}

// Chart configuration for react-native-chart-kit
const chartConfig = {
  backgroundColor: "#ffffff",
  backgroundGradientFrom: "#ffffff",
  backgroundGradientTo: "#ffffff",
  decimalPlaces: 2,
  color: (opacity = 1) => `rgba(99, 102, 241, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16
  },
  propsForDots: {
    r: "6",
    strokeWidth: "2",
    stroke: "#6366F1"
  }
};

const ChartContainer = React.forwardRef<View, ChartContainerProps>(
  ({ className, children, config, ...props }, ref) => {
    return (
      <ChartContext.Provider value={{ config }}>
        <View
          ref={ref}
          className={cn("flex-1 justify-center items-center p-4", className)}
          {...props}
        >
          {children}
        </View>
      </ChartContext.Provider>
    );
  }
);
ChartContainer.displayName = "Chart";

// ChartStyle is not needed in React Native

// Chart components using react-native-chart-kit
interface ChartLineProps {
  data: {
    labels: string[];
    datasets: Array<{
      data: number[];
      color?: (opacity: number) => string;
      strokeWidth?: number;
    }>;
  };
  width?: number;
  height?: number;
  className?: string;
}

const ChartLine = React.forwardRef<View, ChartLineProps>(
  ({ data, width, height = 220, className, ...props }, ref) => {
    const screenWidth = width || Dimensions.get("window").width - 40;

    return (
      <View ref={ref} className={cn("", className)} {...props}>
        <LineChart
          data={data}
          width={screenWidth}
          height={height}
          chartConfig={chartConfig}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
        />
      </View>
    );
  }
);
ChartLine.displayName = "ChartLine";

interface ChartBarProps {
  data: {
    labels: string[];
    datasets: Array<{
      data: number[];
    }>;
  };
  width?: number;
  height?: number;
  className?: string;
  yAxisLabel?: string;
  yAxisSuffix?: string;
}

const ChartBar = React.forwardRef<View, ChartBarProps>(
  ({ data, width, height = 220, className, yAxisLabel = "", yAxisSuffix = "", ...props }, ref) => {
    const screenWidth = width || Dimensions.get("window").width - 40;

    return (
      <View ref={ref} className={cn("", className)} {...props}>
        <BarChart
          data={data}
          width={screenWidth}
          height={height}
          yAxisLabel={yAxisLabel}
          yAxisSuffix={yAxisSuffix}
          chartConfig={chartConfig}
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
        />
      </View>
    );
  }
);
ChartBar.displayName = "ChartBar";

interface ChartPieProps {
  data: Array<{
    name: string;
    population: number;
    color: string;
    legendFontColor?: string;
    legendFontSize?: number;
  }>;
  width?: number;
  height?: number;
  className?: string;
}

const ChartPie = React.forwardRef<View, ChartPieProps>(
  ({ data, width, height = 220, className, ...props }, ref) => {
    const screenWidth = width || Dimensions.get("window").width - 40;

    return (
      <View ref={ref} className={cn("", className)} {...props}>
        <PieChart
          data={data}
          width={screenWidth}
          height={height}
          chartConfig={chartConfig}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
        />
      </View>
    );
  }
);
ChartPie.displayName = "ChartPie";

// Simple tooltip component for React Native
interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

interface ChartTooltipContentProps {
  active?: boolean;
  payload?: any[];
  className?: string;
  indicator?: "dot" | "line" | "dashed";
  hideLabel?: boolean;
  hideIndicator?: boolean;
  label?: string;
  labelFormatter?: (value: any, payload: any[]) => React.ReactNode;
  labelClassName?: string;
  formatter?: any;
  color?: string;
  nameKey?: string;
}

const ChartTooltipContent = React.forwardRef<View, ChartTooltipContentProps>(
  (
    {
      active,
      payload,
      className,
      indicator = "dot",
      hideLabel = false,
      hideIndicator = false,
      label,
      labelFormatter,
      labelClassName,
      color,
      nameKey,
    },
    ref
  ) => {
    const { config } = useChart();

    if (!active || !payload?.length) return null;

    return (
      <View
        ref={ref}
        className={cn(
          "bg-white p-3 rounded-lg border border-gray-200 shadow-lg",
          className
        )}
      >
        {!hideLabel && label && (
          <Text className={cn("font-medium text-gray-900 mb-2", labelClassName)}>
            {labelFormatter ? labelFormatter(label, payload) : label}
          </Text>
        )}

        {payload.map((item: any, index: number) => {
          const key = `${nameKey || item.name || item.dataKey || "value"}`;
          const itemConfig = config[key];
          const indicatorColor = color || item.color || "#6366F1";

          return (
            <View key={index} className="flex-row items-center mb-1">
              {!hideIndicator && (
                <View
                  className={cn("rounded mr-2", {
                    "w-2 h-2": indicator === "dot",
                    "w-1 h-4": indicator === "line",
                  })}
                  style={{ backgroundColor: indicatorColor }}
                />
              )}
              <Text className="text-gray-700 flex-1">
                {itemConfig?.label || item.name}: {item.value}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }
);
ChartTooltipContent.displayName = "ChartTooltip";

// Simple legend component for React Native
interface ChartLegendProps {
  payload?: any[];
  className?: string;
  hideIcon?: boolean;
  verticalAlign?: "top" | "bottom";
  nameKey?: string;
}

const ChartLegend: React.FC<ChartLegendProps> = () => {
  return null; // Placeholder for legend functionality
};

// Simple tooltip component for React Native
const ChartTooltip: React.FC<ChartTooltipProps> = () => {
  return null; // Placeholder for tooltip functionality
};

interface ChartLegendContentProps {
  className?: string;
  hideIcon?: boolean;
  payload?: any[];
  verticalAlign?: "top" | "bottom";
  nameKey?: string;
}

const ChartLegendContent = React.forwardRef<View, ChartLegendContentProps>(
  ({ className, hideIcon = false, payload, verticalAlign = "bottom", nameKey }, ref) => {
    const { config } = useChart();
    if (!payload?.length) return null;

    return (
      <View
        ref={ref}
        className={cn(
          "flex-row items-center justify-center gap-4",
          verticalAlign === "top" ? "pb-3" : "pt-3",
          className
        )}
      >
        {payload.map((item: any, index: number) => {
          const key = `${nameKey || item.dataKey || "value"}`;
          const itemConfig = config[key];
          return (
            <View key={index} className="flex-row items-center gap-1.5">
              {!hideIcon && (
                <View
                  className="h-2 w-2 rounded"
                  style={{ backgroundColor: item.color || "#6366F1" }}
                />
              )}
              <Text className="text-sm text-gray-700">
                {itemConfig?.label || item.value}
              </Text>
            </View>
          );
        })}
      </View>
    );
  }
);
ChartLegendContent.displayName = "ChartLegend";

export {
    ChartBar, chartConfig, ChartContainer, ChartLegend,
    ChartLegendContent, ChartLine, ChartPie,
    ChartTooltip,
    ChartTooltipContent
};

