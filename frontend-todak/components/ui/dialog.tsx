// components/ui/dialog.tsx
import { cn } from "@/lib/utils";
import { X } from "lucide-react-native";
import React, { ReactNode } from "react";
import {
    Modal,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from "react-native";

type DialogProps = {
  visible: boolean;
  onClose: () => void;
  children: ReactNode;
  hideCloseButton?: boolean;
};

export const Dialog = ({
  visible,
  onClose,
  children,
  hideCloseButton = false,
}: DialogProps) => {
  return (
    <Modal transparent animationType="fade" visible={visible}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View className="flex-1 bg-black/60 justify-center items-center px-4">
          <TouchableWithoutFeedback>
            <View className="bg-white dark:bg-black w-full max-w-lg rounded-lg p-6 shadow-lg relative">
              {!hideCloseButton && (
                <TouchableOpacity
                  onPress={onClose}
                  className="absolute right-4 top-4 p-1 rounded-md opacity-70"
                >
                  <X size={18} className="text-muted-foreground" />
                </TouchableOpacity>
              )}
              {children}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export const DialogHeader = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <View className={cn("mb-4 space-y-1 text-center sm:text-left", className)}>
    {children}
  </View>
);

export const DialogFooter = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <View className={cn("mt-6 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className)}>
    {children}
  </View>
);

export const DialogTitle = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <Text className={cn("text-lg font-semibold text-foreground", className)}>
    {children}
  </Text>
);

export const DialogDescription = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <Text className={cn("text-sm text-muted-foreground", className)}>{children}</Text>
);
