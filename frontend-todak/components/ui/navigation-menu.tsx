import { ChevronDown } from "lucide-react-native"
import React, { useRef, useState } from "react"
import {
  Animated,
  Dimensions,
  Pressable,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  View
} from "react-native"

const screenWidth = Dimensions.get("window").width

const NavigationMenu = ({ children }: { children: React.ReactNode }) => {
  return (
    <View className="relative z-10 w-full items-center justify-center">
      {children}
    </View>
  )
}

const NavigationMenuList = ({ children }: { children: React.ReactNode }) => {
  return (
    <View className="flex flex-row items-center justify-center space-x-2">
      {children}
    </View>
  )
}

const NavigationMenuItem = ({ children }: { children: React.ReactNode }) => {
  return <View className="relative">{children}</View>
}

const NavigationMenuTrigger = ({
  label,
  children,
}: {
  label: string
  children: React.ReactNode
}) => {
  const [open, setOpen] = useState(false)
  const rotateAnim = useRef(new Animated.Value(0)).current

  const toggleMenu = () => {
    Animated.timing(rotateAnim, {
      toValue: open ? 0 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start()
    setOpen(!open)
  }

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "180deg"],
  })

  return (
    <View>
      <Pressable
        onPress={toggleMenu}
        className="flex flex-row items-center rounded-md bg-white px-4 py-2 shadow-md"
      >
        <Text className="text-sm font-medium text-black mr-1">{label}</Text>
        <Animated.View style={{ transform: [{ rotate }] }}>
          <ChevronDown size={16} color="black" />
        </Animated.View>
      </Pressable>
      {open && (
        <OutsidePressHandler onClose={() => setOpen(false)}>
          <NavigationMenuContent>{children}</NavigationMenuContent>
        </OutsidePressHandler>
      )}
    </View>
  )
}

const NavigationMenuContent = ({
  children,
}: {
  children: React.ReactNode
}) => {
  return (
    <View className="absolute left-0 top-full mt-2 w-[200px] rounded-md border bg-white shadow-lg">
      <ScrollView className="max-h-60 p-2">{children}</ScrollView>
    </View>
  )
}

const NavigationMenuSub = ({
  label,
  children,
}: {
  label: string
  children: React.ReactNode
}) => {
  const [open, setOpen] = useState(false)
  const rotateAnim = useRef(new Animated.Value(0)).current

  const toggleSub = () => {
    Animated.timing(rotateAnim, {
      toValue: open ? 0 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start()
    setOpen(!open)
  }

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "180deg"],
  })

  return (
    <View>
      <Pressable
        onPress={toggleSub}
        className="flex flex-row items-center justify-between rounded px-2 py-1"
      >
        <Text className="text-sm text-black">{label}</Text>
        <Animated.View style={{ transform: [{ rotate }] }}>
          <ChevronDown size={14} color="black" />
        </Animated.View>
      </Pressable>
      {open && <View className="pl-4 pt-1">{children}</View>}
    </View>
  )
}

const NavigationMenuLink = ({ label, onPress }: { label: string; onPress: () => void }) => {
  return (
    <Pressable onPress={onPress} className="rounded px-2 py-1">
      <Text className="text-sm text-black">{label}</Text>
    </Pressable>
  )
}

const OutsidePressHandler = ({
  children,
  onClose,
}: {
  children: React.ReactNode
  onClose: () => void
}) => {
  return (
    <TouchableWithoutFeedback onPress={onClose}>
      <View className="absolute inset-0 z-0">
        <TouchableWithoutFeedback>{children}</TouchableWithoutFeedback>
      </View>
    </TouchableWithoutFeedback>
  )
}

export {
  NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuSub, NavigationMenuTrigger
}

