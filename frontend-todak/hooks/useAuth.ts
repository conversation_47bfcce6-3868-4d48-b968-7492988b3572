import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useEffect, useState } from "react";
import Toast from "react-native-toast-message"; // 또는 네이티브용 Toast 훅

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (
    email: string,
    password: string,
    name: string,
    selectedMode: string
  ) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: "frontendtodak://", // 📱 앱 URL 스킴으로 설정
          data: {
            name,
            selected_mode: selectedMode,
          },
        },
      });

      if (error) {
        Toast.show({
          type: "error",
          text1: "회원가입 실패",
          text2: error.message,
        });
        return { error };
      }

      Toast.show({
        type: "success",
        text1: "회원가입 성공",
        text2: "이메일을 확인해주세요.",
      });

      return { error: null };
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "오류",
        text2: "회원가입 중 오류가 발생했습니다.",
      });
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) {
        Toast.show({
          type: "error",
          text1: "로그인 실패",
          text2: error.message,
        });
        return { error };
      }

      Toast.show({
        type: "success",
        text1: "로그인 성공",
        text2: "환영합니다!",
      });

      return { error: null };
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "오류",
        text2: "로그인 중 오류가 발생했습니다.",
      });
      return { error };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        Toast.show({
          type: "error",
          text1: "로그아웃 실패",
          text2: error.message,
        });
        return { error };
      }

      Toast.show({
        type: "success",
        text1: "로그아웃 완료",
        text2: "안전하게 로그아웃되었습니다.",
      });

      return { error: null };
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "오류",
        text2: "로그아웃 중 오류가 발생했습니다.",
      });
      return { error };
    }
  };

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
  };
};
