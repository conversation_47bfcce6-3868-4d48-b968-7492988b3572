
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // 인증 상태 변화 리스너 설정
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    // 현재 세션 확인
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, name: string, selectedMode: string) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/`,
          data: {
            name,
            selected_mode: selectedMode
          }
        }
      });

      if (error) {
        toast({
          variant: "destructive",
          title: "회원가입 실패",
          description: error.message
        });
        return { error };
      }

      toast({
        title: "회원가입 성공",
        description: "이메일을 확인해주세요."
      });

      return { error: null };
    } catch (error) {
      toast({
        variant: "destructive", 
        title: "오류",
        description: "회원가입 중 오류가 발생했습니다."
      });
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          variant: "destructive",
          title: "로그인 실패",
          description: error.message
        });
        return { error };
      }

      toast({
        title: "로그인 성공",
        description: "환영합니다!"
      });

      return { error: null };
    } catch (error) {
      toast({
        variant: "destructive",
        title: "오류", 
        description: "로그인 중 오류가 발생했습니다."
      });
      return { error };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast({
          variant: "destructive",
          title: "로그아웃 실패",
          description: error.message
        });
        return { error };
      }

      toast({
        title: "로그아웃 완료",
        description: "안전하게 로그아웃되었습니다."
      });

      return { error: null };
    } catch (error) {
      toast({
        variant: "destructive",
        title: "오류",
        description: "로그아웃 중 오류가 발생했습니다."
      });
      return { error };
    }
  };

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut
  };
};
