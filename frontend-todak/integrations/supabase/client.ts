// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zmcbgilosipqctmyaxut.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InptY2JnaWxvc2lwcWN0bXlheHV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDE0NDYsImV4cCI6MjA2NjUxNzQ0Nn0.UQarmpE12DhF1GGa1wjmrHGIvqcweUYUTeJCYUVc_e0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);