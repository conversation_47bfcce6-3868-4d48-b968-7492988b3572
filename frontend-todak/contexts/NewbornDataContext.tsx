
import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface NewbornLogEntry {
  id: string;
  type: 'breastfeeding' | 'formula' | 'babyfood' | 'diaper' | 'sleep' | 'pumped' | 'bath' | 'hospital' | 'temperature' | 'medication' | 'snack' | 'milk' | 'water' | 'tummytime' | 'other' | 'recordA' | 'recordB';
  date: string;
  time: string;
  duration?: string;
  amount?: string;
  notes?: string;
  side?: string;
  diaperType?: string;
  temperature?: string;
  medicationName?: string;
  medicationDose?: string;
  customLabel?: string;
}

export interface NewbornNote {
  id: string;
  date: string;
  time: string;
  title?: string;
  content: string;
}

interface NewbornDataContextType {
  logs: NewbornLogEntry[];
  notes: NewbornNote[];
  addLog: (log: Omit<NewbornLogEntry, 'id'>) => void;
  addNote: (note: Omit<NewbornNote, 'id'>) => void;
  getLogsByType: (type: string) => NewbornLogEntry[];
  getLogsByDate: (date: string) => NewbornLogEntry[];
  getTodaysStats: () => {
    feedingCount: number;
    diaperCount: number;
    lastFeeding: NewbornLogEntry | null;
    lastDiaper: NewbornLogEntry | null;
    lastSleep: NewbornLogEntry | null;
  };
}

const NewbornDataContext = createContext<NewbornDataContextType | undefined>(undefined);

export const useNewbornData = () => {
  const context = useContext(NewbornDataContext);
  if (!context) {
    throw new Error('useNewbornData must be used within a NewbornDataProvider');
  }
  return context;
};

export const NewbornDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [logs, setLogs] = useState<NewbornLogEntry[]>([]);
  const [notes, setNotes] = useState<NewbornNote[]>([]);

  const addLog = (logData: Omit<NewbornLogEntry, 'id'>) => {
    const newLog: NewbornLogEntry = {
      ...logData,
      id: Date.now().toString(),
    };
    setLogs(prev => [...prev, newLog]);
    console.log('Added newborn log:', newLog);
  };

  const addNote = (noteData: Omit<NewbornNote, 'id'>) => {
    const newNote: NewbornNote = {
      ...noteData,
      id: Date.now().toString(),
    };
    setNotes(prev => [...prev, newNote]);
    console.log('Added newborn note:', newNote);
  };

  const getLogsByType = (type: string) => {
    return logs.filter(log => log.type === type);
  };

  const getLogsByDate = (date: string) => {
    return logs.filter(log => log.date === date);
  };

  const getTodaysStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayLogs = getLogsByDate(today);
    
    const feedingLogs = todayLogs.filter(log => 
      log.type === 'breastfeeding' || log.type === 'formula' || log.type === 'babyfood' || log.type === 'pumped' || log.type === 'milk'
    );
    const diaperLogs = todayLogs.filter(log => log.type === 'diaper');
    
    const sortedLogs = [...logs].sort((a, b) => {
      const dateTimeA = new Date(`${a.date}T${a.time}`);
      const dateTimeB = new Date(`${b.date}T${b.time}`);
      return dateTimeB.getTime() - dateTimeA.getTime();
    });

    const lastFeeding = sortedLogs.find(log => 
      log.type === 'breastfeeding' || log.type === 'formula' || log.type === 'babyfood' || log.type === 'pumped' || log.type === 'milk'
    ) || null;
    
    const lastDiaper = sortedLogs.find(log => log.type === 'diaper') || null;
    const lastSleep = sortedLogs.find(log => log.type === 'sleep') || null;

    return {
      feedingCount: feedingLogs.length,
      diaperCount: diaperLogs.length,
      lastFeeding,
      lastDiaper,
      lastSleep,
    };
  };

  return (
    <NewbornDataContext.Provider value={{
      logs,
      notes,
      addLog,
      addNote,
      getLogsByType,
      getLogsByDate,
      getTodaysStats,
    }}>
      {children}
    </NewbornDataContext.Provider>
  );
};
