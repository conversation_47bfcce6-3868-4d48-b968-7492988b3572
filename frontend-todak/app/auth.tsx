import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'expo-router';
import { Baby, Heart, Stethoscope } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

const Auth = () => {
  const { user, signIn, signUp, loading } = useAuth();
  const router = useRouter();
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    selectedMode: 'planning',
  });

  useEffect(() => {
    const storedMode = globalThis?.localStorage?.getItem?.('selectedMode');
    if (storedMode) {
      setFormData(prev => ({ ...prev, selectedMode: storedMode }));
    }
  }, []);

  if (user) {
    router.replace('/');
    return null;
  }

  const handleSubmit = async () => {
    if (isSignUp) {
      await signUp(formData.email, formData.password, formData.name, formData.selectedMode);
    } else {
      await signIn(formData.email, formData.password);
    }
  };

  const modes = [
    { id: 'planning', title: '임신 준비', icon: Heart, color: 'text-pink-600' },
    { id: 'pregnancy', title: '임신 중', icon: Stethoscope, color: 'text-purple-600' },
    { id: 'newborn', title: '신생아 돌봄', icon: Baby, color: 'text-blue-600' },
  ];

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#9333ea" />
        <Text className="mt-2 text-gray-500">로딩 중...</Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 px-4 py-8">
      <View className="w-full max-w-md mx-auto bg-white p-6 rounded-xl shadow-md">
        {/* 로고 */}
        <View className="items-center mb-6">
          <View className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 items-center justify-center flex">
            <Heart size={28} color="white" />
          </View>
          <Text className="mt-4 text-2xl font-bold text-gray-900">TodakTodak</Text>
          <Text className="text-gray-500 mt-1">
            {isSignUp ? '새 계정을 만들어보세요' : '계정에 로그인하세요'}
          </Text>
        </View>

        {/* 탭 전환 */}
        <View className="flex-row mb-6">
          <TouchableOpacity
            onPress={() => setIsSignUp(false)}
            className={`flex-1 py-2 items-center rounded-l-lg ${!isSignUp ? 'bg-purple-600' : 'bg-gray-100'}`}
          >
            <Text className={`${!isSignUp ? 'text-white' : 'text-gray-600'} font-semibold`}>로그인</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setIsSignUp(true)}
            className={`flex-1 py-2 items-center rounded-r-lg ${isSignUp ? 'bg-purple-600' : 'bg-gray-100'}`}
          >
            <Text className={`${isSignUp ? 'text-white' : 'text-gray-600'} font-semibold`}>회원가입</Text>
          </TouchableOpacity>
        </View>

        {/* 폼 */}
        <View className="space-y-4">
          {isSignUp && (
            <View>
              <Text className="text-sm text-gray-700 mb-1">이름</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                value={formData.name}
                onChangeText={text => setFormData(prev => ({ ...prev, name: text }))}
                placeholder="이름"
              />
            </View>
          )}

          <View>
            <Text className="text-sm text-gray-700 mb-1">이메일</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              value={formData.email}
              onChangeText={text => setFormData(prev => ({ ...prev, email: text }))}
              placeholder="이메일"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View>
            <Text className="text-sm text-gray-700 mb-1">비밀번호</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              value={formData.password}
              onChangeText={text => setFormData(prev => ({ ...prev, password: text }))}
              placeholder="비밀번호"
              secureTextEntry
            />
          </View>

          {isSignUp && (
            <View>
              <Text className="text-sm text-gray-700 mb-2">사용 목적</Text>
              <View className="space-y-2">
                {modes.map(mode => (
                  <TouchableOpacity
                    key={mode.id}
                    onPress={() => setFormData(prev => ({ ...prev, selectedMode: mode.id }))}
                    className={`flex-row items-center space-x-2 border rounded-lg px-3 py-2 ${
                      formData.selectedMode === mode.id ? 'border-purple-600' : 'border-gray-300'
                    }`}
                  >
                    <mode.icon size={18} className={`${mode.color}`} />
                    <Text className="text-sm text-gray-800">{mode.title}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          <TouchableOpacity
            onPress={handleSubmit}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 py-3 rounded-lg items-center mt-2"
          >
            <Text className="text-white font-medium">{isSignUp ? '회원가입' : '로그인'}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

export default Auth;
