import SignupScreen from "@/components/SignupScreen"
// import DefaultLayout from "@/components/layouts/DefaultLayout"
// import NewbornLayout from "@/components/layouts/NewbornLayout"
import ModeSelector from "@/components/ModeSelector"
import OnboardingFlow from "@/components/OnboardingFlow"
import SplashScreen from "@/components/SplashScreen"
import { Button } from "@/components/ui/button"
import { useAppLogic, type Mode } from "@/hooks/useAppLogic"
import { useNavigation } from "@react-navigation/native"
import { LogOut, User } from "lucide-react-native"
import React from "react"
import { ScrollView, Text, TouchableOpacity, View } from "react-native"

const Placeholder = ({ title }: { title: string }) => (
  <View className="p-6 bg-white rounded-lg shadow-md items-center">
    <Text className="text-lg font-semibold text-gray-800">{title} 화면은 준비 중입니다.</Text>
  </View>
)

const Index = () => {
  const navigation = useNavigation()
  // const { user, loading, signOut } = useAuth()

  const {
    showSplash,
    currentMode,
    setCurrentMode,
    showOnboarding,
    showSignup,
    currentLanguage,
    selectedDate,
    t,
    handleOnboardingComplete,
    handleSignupComplete,
    toggleLanguage,
  } = useAppLogic()


  if (showSplash) {
    return <SplashScreen />
  }

  if (showOnboarding) {
    return (
      <OnboardingFlow
        onComplete={handleOnboardingComplete}
        language={currentLanguage}
        toggleLanguage={toggleLanguage}
        t={t}
      />
    )
  }

  if (showSignup) {
    return (
      <SignupScreen 
        onComplete={handleSignupComplete}
        selectedMode={currentMode}
        language={currentLanguage}
        t={t}
      />
    );
  }

  return (
    <ScrollView className="flex-1 bg-gray-100 pt-4">
      {/* 상단 네비게이션 바 */}
      <View className="mx-4 mb-4">
        <View className="flex-row items-center justify-between bg-white rounded-lg shadow-sm p-4">
          <View className="flex-row items-center space-x-3">
            <View className="w-8 h-8 bg-purple-600 rounded-full items-center justify-center">
              <Text className="text-white text-sm font-bold text-center">T</Text>
            </View>
            <Text className="text-lg font-semibold text-gray-900">TodakTodak</Text>
          </View>

          <View className="flex-row space-x-2">
            <TouchableOpacity onPress={() => navigation.navigate("Profile" as never)}>
              <Button variant="ghost" size="sm" className="flex-row items-center">
                <User size={16} className="mr-1" />
                <Text>프로필</Text>
              </Button>
            </TouchableOpacity>
            <Button >
              <LogOut size={16} className="mr-1" />
              <Text>로그아웃</Text>
            </Button>
          </View>
        </View>
      </View>

      <View className="mx-4 space-y-6">
        <ModeSelector
          currentMode={currentMode}
          onModeChange={(mode: string) => setCurrentMode(mode as Mode)}
          t={t}
        />

        {/* 콘텐츠 영역 */}
        <Placeholder title={currentMode === "newborn" ? "신생아 모드" : "기본 모드"} />
      </View>
    </ScrollView>
  )
}

export default Index
