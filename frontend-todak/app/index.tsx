// screens/HomeScreen.tsx
import OnboardingFlow from '@/components/OnboardingFlow';
import SplashScreen from '@/components/SplashScreen';
import { useAppLogic } from '@/hooks/useAppLogic';
import React from 'react';
import { Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
// import SignupScreen from '@/components/SignupScreen';
// import DefaultLayout from '@/components/layouts/DefaultLayout';
// import NewbornLayout from '@/components/layouts/NewbornLayout';

const HomeScreen = () => {
  const {
    showSplash,
    currentMode,
    setCurrentMode,
    showOnboarding,
    showSignup,
    currentLanguage,
    setCurrentLanguage,
    selectedDate,
    t,
    handleOnboardingComplete,
    handleSignupComplete,
    toggleLanguage,
  } = useAppLogic();

  if (showSplash) return <SplashScreen />;

  if (showOnboarding) {
    return (
      <OnboardingFlow
        onComplete={handleOnboardingComplete}
        language={currentLanguage}
        toggleLanguage={toggleLanguage}
        t={t}
      />
    );
  }

  return (
    <SafeAreaView className="flex-1 items-center justify-center bg-pink-50">
      <Text className="text-3xl font-bold text-pink-600 mb-4">
        Welcome to NativeWind!
      </Text>
      <Text className="text-lg text-gray-600 mb-8">
        Tailwind CSS is working! 🎉
      </Text>
      <Text className="text-base bg-blue-500 text-white px-6 py-3 rounded-lg">
        This is a styled button
      </Text>
      {/* 모드 선택 등 추가 UI는 여기서 계속 확장 가능 */}
    </SafeAreaView>
  );
};

export default HomeScreen;
