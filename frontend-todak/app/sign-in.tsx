import { useAuth } from '@/hooks/useAuth';
import { router } from 'expo-router';
import { Heart } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Text, TextInput, TouchableOpacity, View } from 'react-native';

const SignIn = () => {
  const { user, signIn, loading } = useAuth();
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (isSubmitting) return;

    if (!formData.email || !formData.password) {
      Alert.alert('오류', '이메일과 비밀번호를 입력해주세요.');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await signIn(formData.email, formData.password);
      if (!result?.error) {
        // 로그인 성공 시 메인 화면으로 이동
        router.replace('/');
      }
    } catch (error) {
      Alert.alert('오류', '로그인 중 문제가 발생했습니다.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 이미 로그인된 사용자는 메인 화면으로 리다이렉트
  useEffect(() => {
    if (user) {
      router.replace('/');
    }
  }, [user]);

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#9333ea" />
        <Text className="mt-2 text-gray-500">로딩 중...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 items-center justify-center p-4">
      <View className="w-full max-w-md bg-white p-6 rounded-xl shadow-lg">
        {/* 로고 */}
        <View className="items-center mb-6">
          <View className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 items-center justify-center flex">
            <Heart size={28} color="white" />
          </View>
          <Text className="mt-4 text-2xl font-bold text-gray-900">TodakTodak</Text>
          <Text className="text-gray-500 mt-1">계정에 로그인하세요</Text>
        </View>

        {/* 폼 */}
        <View className="space-y-4">
          <View>
            <Text className="text-sm text-gray-700 mb-1">이메일</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholder="<EMAIL>"
            />
          </View>

          <View>
            <Text className="text-sm text-gray-700 mb-1">비밀번호</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              value={formData.password}
              onChangeText={(text) => setFormData(prev => ({ ...prev, password: text }))}
              secureTextEntry
              placeholder="비밀번호"
            />
          </View>

          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isSubmitting}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 py-3 rounded-lg items-center"
            style={{ opacity: isSubmitting ? 0.7 : 1 }}
          >
            {isSubmitting ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text className="text-white font-medium">로그인</Text>
            )}
          </TouchableOpacity>

          <View className="flex-row justify-center mt-2">
            <Text className="text-sm text-gray-500">계정이 없으신가요? </Text>
            <TouchableOpacity onPress={() => router.push('/sign-up')}>
              <Text className="text-sm text-purple-600 font-medium ml-1">회원가입</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SignIn;
