import OnboardingFlow from '@/components/OnboardingFlow';
import { useAppLogic } from '@/hooks/useAppLogic';
import React from 'react';

const WelcomeScreen = () => {
  const { handleOnboardingComplete, currentLanguage, toggleLanguage, t } = useAppLogic();

  return (
    <OnboardingFlow
      onComplete={handleOnboardingComplete}
      language={currentLanguage}
      toggleLanguage={toggleLanguage}
      t={t}
    />
  );
};

export default WelcomeScreen;
