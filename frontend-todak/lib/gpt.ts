// lib/gpt.ts
import axios from "axios";

export async function fetchChatGPTResponse(userMessage: string, mode: string): Promise<string> {
  const systemPrompt = {
    planning: "You are a fertility health assistant helping women plan pregnancy.",
    pregnancy: "You are a supportive assistant guiding users through pregnancy.",
    newborn: "You help new moms understand baby behavior and give gentle advice."
  }[mode] || "You are a helpful assistant.";

  const apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY; // .env에 설정해 주세요

  const response = await axios.post(
    "https://api.openai.com/v1/chat/completions",
    {
      model: "gpt-4", // 또는 gpt-3.5-turbo
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userMessage }
      ],
      temperature: 0.7,
    },
    {
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json"
      }
    }
  );

  return response.data.choices[0].message.content.trim();
}
